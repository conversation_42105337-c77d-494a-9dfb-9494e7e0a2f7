llama-cpp-migration/
.git/
.gitignore
*.md
*.tmp
.DS_Store
node_modules/
.env*
# Large directories to exclude
venv/
venv_*/
__pycache__/
*.pyc
webui-data/
backend/
frontend/
docling_env/
enhanced_table_env/
qdrant_indexer_env/
oracle_wallet/
oracle-init/
nginx/
traefik/
screenshots/
jina_backup_*/
memory_backup/
webui_backups/
data/
logs/
*.log
*.db
*.tar.gz
*.rpm
ollama-*/
pandas-container/
mem0-owui/
mcp-integration/
unified-mcpo/
docling-serve/
