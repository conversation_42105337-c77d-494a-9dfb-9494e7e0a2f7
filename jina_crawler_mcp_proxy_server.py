#!/usr/bin/env python3
"""
Jina Crawler MCP OpenAPI Proxy Server
Compatible with Open WebUI's tool system
Based on successful MCPO pattern
"""

import asyncio
import sys
import os
import logging
import re
import aiohttp
from urllib.parse import urljoin, urlparse
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from datetime import datetime
from typing import List, Optional, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add mcp-integration path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mcp-integration/servers/jina_crawler'))

try:
    from jini_crawler import JiniCrawler
    from paywall_bypass_crawler import PaywallBypassCrawler
    from ai_search.ai_search_engine import AISearchEngine
except ImportError as e:
    print(f"Warning: Could not import jina_crawler modules: {e}")
    print("Will use mock implementations")
    JiniCrawler = None
    PaywallBypassCrawler = None
    AISearchEngine = None

app = FastAPI(
    title="MCP OpenAPI Proxy",
    description="""Automatically generated API from MCP Tool Schemas

- **available tools**：
    - [jina_crawler](/jina_crawler/docs)""",
    version="1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request models
class CrawlRequest(BaseModel):
    url: str
    method: Optional[str] = "tls_bypass"
    process_content: Optional[bool] = True
    max_content_length: Optional[int] = 200000  # Leverage 1M input capacity

class SearchRequest(BaseModel):
    query: str
    max_sources: Optional[int] = 15  # More comprehensive results
    enable_query_refinement: Optional[bool] = True

class BatchCrawlRequest(BaseModel):
    urls: List[str]
    method: Optional[str] = "tls_bypass"
    max_content_length: Optional[int] = 150000  # Balanced for batch processing
    max_concurrent: Optional[int] = 6  # Optimized for batch efficiency
    quality_mode: Optional[str] = "detailed"  # Default to detailed for best quality
    processing_mode: Optional[str] = "smart"  # Smart selection between batch/individual
    auto_discover_rss: Optional[bool] = True  # Auto discover and use RSS feeds if available (default: True for better news coverage)
    prefer_rss: Optional[bool] = True  # Prefer RSS feeds over homepage when discovered
    context: Optional[str] = "general"  # Context: "ai_search", "news_aggregation", "general"

class RSSDiscoveryRequest(BaseModel):
    urls: List[str]
    auto_crawl_rss: Optional[bool] = True  # Auto crawl discovered RSS feeds
    max_rss_per_site: Optional[int] = 5  # Max RSS feeds to discover per site
    quality_mode: Optional[str] = "detailed"
    processing_mode: Optional[str] = "batch"

class PaywallBypassRequest(BaseModel):
    url: str
    max_content_length: Optional[int] = 300000  # Much higher for premium content

class FullArticleRequest(BaseModel):
    url: str
    max_content_length: Optional[int] = 800000  # Near 1M token limit
    extract_metadata: Optional[bool] = True

class NewsSummaryRequest(BaseModel):
    url: str
    max_content_length: Optional[int] = 400000  # For comprehensive news
    include_analysis: Optional[bool] = True

class EnhancedExtractionRequest(BaseModel):
    url: str
    max_content_length: Optional[int] = 600000  # For detailed extraction
    extract_metadata: Optional[bool] = True
    include_analysis: Optional[bool] = True

class OptimizedBatchRequest(BaseModel):
    urls: List[str]
    target_llm: str = "gpt-4"  # Target LLM for optimization
    max_total_output: Optional[int] = None  # Auto-calculated based on target_llm
    max_concurrent: Optional[int] = 6
    include_summary: Optional[bool] = True

# Initialize components
crawler = None
paywall_crawler = None
ai_search_engine = None

if JiniCrawler:
    try:
        crawler = JiniCrawler()
        print("✅ JiniCrawler initialized")
    except Exception as e:
        print(f"❌ Failed to initialize JiniCrawler: {e}")

if PaywallBypassCrawler:
    try:
        paywall_crawler = PaywallBypassCrawler()
        print("✅ PaywallBypassCrawler initialized")
    except Exception as e:
        print(f"❌ Failed to initialize PaywallBypassCrawler: {e}")

if AISearchEngine:
    try:
        ai_search_engine = AISearchEngine()
        print("✅ AISearchEngine initialized")
    except Exception as e:
        print(f"❌ Failed to initialize AISearchEngine: {e}")

@app.get("/")
async def root():
    return {
        "openapi": "3.1.0",
        "info": {
            "title": "MCP OpenAPI Proxy",
            "description": "Automatically generated API from MCP Tool Schemas\n\n- **available tools**：\n    - [jina_crawler](/jina_crawler/docs)",
            "version": "1.0"
        },
        "paths": {}
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "tools": ["jina_crawler"],
        "components": {
            "crawler": crawler is not None,
            "paywall_crawler": paywall_crawler is not None,
            "ai_search_engine": ai_search_engine is not None
        },
        "timestamp": datetime.now().isoformat()
    }

# Jina Crawler tool endpoints following MCP pattern
@app.get("/jina_crawler/docs")
async def jina_crawler_docs():
    """Swagger UI for jina_crawler tool"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
    <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css">
    <link rel="shortcut icon" href="https://fastapi.tiangolo.com/img/favicon.png">
    <title>jina_crawler - Swagger UI</title>
    </head>
    <body>
    <div id="swagger-ui">
    </div>
    <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js"></script>
    <script>
    const ui = SwaggerUIBundle({
        url: '/jina_crawler/openapi.json',
        "dom_id": "#swagger-ui",
        "layout": "BaseLayout",
        "deepLinking": true,
        "showExtensions": true,
        "showCommonExtensions": true
    })
    </script>
    </body>
    </html>
    """

@app.get("/jina_crawler/openapi.json")
async def jina_crawler_openapi():
    return {
        "openapi": "3.1.0",
        "info": {
            "title": "jina_crawler",
            "description": "Smart web crawler with AI-powered content processing. Use this tool when users ask to crawl websites, extract content, search the web, or analyze web pages.",
            "version": "1.0.0"
        },
        "paths": {
            "/crawl_url": {
                "post": {
                    "summary": "Crawl and extract content from a website",
                    "description": "Use this tool when users ask to: crawl a website, get content from a URL, extract text from a webpage, read a website, or analyze web content.",
                    "operationId": "crawl_url",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "url": {
                                            "type": "string",
                                            "description": "The website URL to crawl",
                                            "example": "https://dantri.com.vn"
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length",
                                            "default": 10000
                                        }
                                    },
                                    "required": ["url"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successfully crawled website",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "content": {"type": "string"},
                                            "title": {"type": "string"},
                                            "url": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },

            "/crawl_batch": {
                "post": {
                    "summary": "Crawl multiple websites",
                    "description": "Use this tool when users ask to crawl multiple URLs or compare content from several websites.",
                    "operationId": "crawl_batch",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "urls": {
                                            "type": "array",
                                            "items": {"type": "string"},
                                            "description": "List of URLs to crawl"
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length per URL",
                                            "default": 10000
                                        },
                                        "max_concurrent": {
                                            "type": "integer",
                                            "description": "Maximum number of concurrent crawl operations",
                                            "default": 10,
                                            "minimum": 1,
                                            "maximum": 20
                                        }
                                    },
                                    "required": ["urls"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Batch crawl results",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "results": {"type": "array"},
                                            "total_urls": {"type": "integer"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/bypass_paywall": {
                "post": {
                    "summary": "Bypass paywall and extract premium content",
                    "description": "Use this tool when users ask to read content from paywall-protected websites, premium articles, or subscription-based content.",
                    "operationId": "bypass_paywall",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "url": {
                                            "type": "string",
                                            "description": "Paywall-protected URL to extract content from",
                                            "example": "https://premium-site.com/article"
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length",
                                            "default": 50000
                                        }
                                    },
                                    "required": ["url"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Successfully bypassed paywall and extracted content",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "content": {"type": "string"},
                                            "title": {"type": "string"},
                                            "url": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/ai_search": {
                "post": {
                    "summary": "Multi-Source AI-powered web search with comprehensive analysis",
                    "description": "Use this tool when users ask to search the internet, find information online, research topics, get latest news, or analyze web content. Uses 3 parallel search sources (Google PSE + DuckDuckGo + Brave) for comprehensive coverage. Provides AI-processed results with content synthesis and citations.",
                    "operationId": "ai_search",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "query": {
                                            "type": "string",
                                            "description": "Search query",
                                            "example": "latest AI developments 2024"
                                        },
                                        "max_sources": {
                                            "type": "integer",
                                            "description": "Maximum number of sources to search",
                                            "default": 10
                                        },
                                        "enable_query_refinement": {
                                            "type": "boolean",
                                            "description": "Enable AI query refinement",
                                            "default": True
                                        }
                                    },
                                    "required": ["query"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "AI search results with processed content",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "results": {"type": "array"},
                                            "query": {"type": "string"},
                                            "refined_query": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/crawl_full_article": {
                "post": {
                    "summary": "Crawl complete article content with metadata",
                    "description": "Use this tool when users ask to get the full content of a news article, blog post, or detailed webpage. Extracts complete text with metadata like author, date, and tags.",
                    "operationId": "crawl_full_article",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "url": {
                                            "type": "string",
                                            "description": "URL of the article to crawl",
                                            "example": "https://example.com/article"
                                        },
                                        "max_content_length": {
                                            "type": "integer",
                                            "description": "Maximum content length to extract",
                                            "default": 25000
                                        },
                                        "extract_metadata": {
                                            "type": "boolean",
                                            "description": "Extract article metadata (author, date, tags)",
                                            "default": True
                                        }
                                    },
                                    "required": ["url"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Full article content with metadata",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "success": {"type": "boolean"},
                                            "content": {"type": "string"},
                                            "title": {"type": "string"},
                                            "url": {"type": "string"},
                                            "metadata": {"type": "object"},
                                            "processing_time": {"type": "number"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

@app.post("/jina_crawler/crawl_url")
async def crawl_url(request: CrawlRequest):
    """Crawl a single URL - MCP compatible endpoint"""
    if not crawler:
        return {"success": False, "error": "Crawler not available", "content": "Mock crawl result for: " + request.url}

    try:
        start_time = datetime.now()

        # Initialize crawler if needed
        if not crawler._initialized:
            await crawler.initialize()

        # Use the actual crawler with correct method name
        result = await crawler.crawl_and_process(
            url=request.url,
            max_content_length=request.max_content_length
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": result.success,
            "content": result.processed_content or result.raw_content or "",
            "title": result.title or "",
            "url": result.url,
            "processing_time": processing_time,
            "method": request.method,
            "error": result.error if not result.success else None
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": request.url,
            "content": f"Error crawling {request.url}: {str(e)}"
        }


@app.post("/jina_crawler/crawl_batch")
async def crawl_batch(request: BatchCrawlRequest):
    """Crawl multiple URLs - MCP compatible endpoint"""
    if not crawler:
        return {
            "success": False,
            "error": "Crawler not available",
            "results": [{"url": url, "content": f"Mock result for {url}"} for url in request.urls]
        }

    try:
        start_time = datetime.now()

        logger.info(f"🔄 Starting batch crawl for {len(request.urls)} URLs")
        logger.info(f"🔧 Processing mode: {request.processing_mode}")
        logger.info(f"🔧 Quality mode: {request.quality_mode}")
        logger.info(f"🔧 Auto discover RSS: {request.auto_discover_rss} (context: {request.context})")
        if request.auto_discover_rss and request.context == "ai_search":
            logger.info("ℹ️ RSS discovery disabled for AI search context")

        # Initialize crawler if needed
        if not crawler._initialized:
            await crawler.initialize()

        # Step 1: Auto-discover RSS feeds if enabled (but not for AI search)
        final_urls = request.urls.copy()

        # Smart detection: Auto-enable RSS discovery for news websites
        news_domains = ["dantri.com", "vnexpress.net", "tuoitre.vn", "thanhnien.vn", "vietnamnet.vn", "tienphong.vn", "laodong.vn", "baomoi.com"]
        has_news_domain = any(domain in url.lower() for url in request.urls for domain in news_domains)

        if has_news_domain and request.context == "general":
            logger.info(f"🔍 News domain detected, auto-enabling RSS discovery")
            should_discover_rss = True
        else:
            should_discover_rss = request.auto_discover_rss and request.context != "ai_search"

        if should_discover_rss:
            logger.info(f"🔍 Auto-discovering RSS feeds for {len(request.urls)} URLs")
            enhanced_urls = []

            for url in request.urls:
                try:
                    # For known news domains, try direct RSS URLs first
                    domain_rss_map = {
                        "vnexpress.net": [
                            "https://vnexpress.net/rss/tin-moi-nhat.rss",
                            "https://vnexpress.net/rss/thoi-su.rss",
                            "https://vnexpress.net/rss/kinh-doanh.rss"
                        ],
                        "dantri.com": [
                            "https://dantri.com.vn/rss/home.rss",
                            "https://dantri.com.vn/rss/xa-hoi.rss",
                            "https://dantri.com.vn/rss/tin-moi-nhat.rss"
                        ],
                        "tuoitre.vn": [
                            "https://tuoitre.vn/rss/thoi-su.rss",
                            "https://tuoitre.vn/rss/tin-moi-nhat.rss"
                        ]
                    }

                    # Check if this is a known domain with predefined RSS
                    known_rss = None
                    for domain, rss_list in domain_rss_map.items():
                        if domain in url.lower():
                            known_rss = rss_list[:3]  # Take first 3
                            break

                    if known_rss and request.prefer_rss:
                        # Use predefined RSS feeds for known domains
                        enhanced_urls.extend(known_rss)
                        logger.info(f"✅ Using {len(known_rss)} predefined RSS feeds for {url}")
                    else:
                        # Try to discover RSS feeds dynamically
                        rss_feeds = await discover_rss_feeds(url, max_feeds=3)

                        if rss_feeds and request.prefer_rss:
                            # Use discovered RSS feeds instead of original URL
                            for feed in rss_feeds:
                                enhanced_urls.append(feed["url"])
                            logger.info(f"✅ Found {len(rss_feeds)} RSS feeds for {url}, using RSS instead")
                        else:
                            # No RSS found or prefer_rss=False, use original URL
                            enhanced_urls.append(url)
                            if rss_feeds:
                                logger.info(f"✅ Found {len(rss_feeds)} RSS feeds for {url}, but keeping original URL")
                            else:
                                logger.info(f"ℹ️ No RSS feeds found for {url}, using original URL")

                except Exception as e:
                    logger.warning(f"⚠️ RSS discovery failed for {url}: {e}, using original URL")
                    enhanced_urls.append(url)

            final_urls = enhanced_urls
            logger.info(f"🔄 Final URL list: {len(final_urls)} URLs (original: {len(request.urls)})")

        # Smart processing mode selection with fallback
        processing_mode = request.processing_mode
        if processing_mode == "smart":
            # Auto-select based on URL count and quality mode
            if len(final_urls) <= 3 and request.quality_mode == "detailed":
                processing_mode = "individual"
                logger.info(f"🎯 Smart mode: Using individual processing for {len(final_urls)} URLs (≤3 + detailed)")
            else:
                processing_mode = "batch"
                logger.info(f"🎯 Smart mode: Using batch processing for {len(final_urls)} URLs (>3 or balanced)")

        logger.info(f"🔄 Processing mode: {processing_mode}")

        # Define force_detailed for all modes
        force_detailed = (request.quality_mode == "detailed")

        batch_results = []

        if processing_mode == "individual":
            # Process each URL individually with concurrent processing for maximum quality
            logger.info(f"🔄 Individual concurrent processing: {len(final_urls)} URLs")

            async def process_single_url(url: str, index: int) -> Any:
                """Process a single URL with error handling"""
                try:
                    logger.info(f"🔄 Processing URL {index}/{len(final_urls)}: {url}")
                    result = await crawler.crawl_and_process(
                        url=url,
                        max_content_length=request.max_content_length
                    )
                    logger.info(f"✅ Completed URL {index}/{len(final_urls)}")
                    return result
                except Exception as e:
                    logger.error(f"❌ Error processing URL {index}/{len(final_urls)} ({url}): {e}")
                    # Create error result
                    error_result = type('JiniCrawlResult', (), {
                        'success': False,
                        'url': url,
                        'error': str(e),
                        'processed_content': f'Error processing {url}: {str(e)}',
                        'raw_content': '',
                        'title': 'Processing Error'
                    })()
                    return error_result

            # Process all URLs concurrently
            import asyncio
            tasks = [
                process_single_url(url, i+1)
                for i, url in enumerate(final_urls)
            ]
            batch_results = await asyncio.gather(*tasks)
            logger.info(f"✅ Individual concurrent processing completed: {len(batch_results)} results")

        else:
            # Try batch processing first, fallback to individual if fails
            try:
                logger.info(f"🔄 Attempting batch processing for {len(final_urls)} URLs")
                logger.info(f"🔧 Force detailed mode: {force_detailed}")

                # Step 1: Crawl all URLs to get raw content
                batch_raw_data = await crawler.crawl_batch_raw(
                    urls=final_urls,
                    max_content_length=request.max_content_length,
                    max_concurrent=request.max_concurrent
                )
                logger.info(f"✅ Raw crawling completed: {len(batch_raw_data)} results")

                # Step 2: Process with Gemini in batch
                batch_results = await crawler.process_batch_with_gemini(
                    batch_raw_data,
                    force_detailed=force_detailed
                )
                logger.info(f"✅ Batch Gemini processing completed: {len(batch_results)} results")

            except Exception as e:
                logger.error(f"❌ Batch processing failed: {e}")
                logger.error(f"❌ Exception type: {type(e).__name__}")
                import traceback
                logger.error(f"❌ Full traceback: {traceback.format_exc()}")

                # Fallback to individual processing
                logger.info(f"🔄 Falling back to individual processing for {len(final_urls)} URLs")
                batch_results = []
                for i, url in enumerate(final_urls, 1):
                    try:
                        logger.info(f"🔄 Fallback processing URL {i}/{len(request.urls)}: {url}")
                        result = await crawler.crawl_and_process(
                            url=url,
                            max_content_length=request.max_content_length
                        )
                        batch_results.append(result)
                        logger.info(f"✅ Fallback completed URL {i}/{len(request.urls)}")
                    except Exception as fallback_e:
                        logger.error(f"❌ Fallback error for URL {i}/{len(request.urls)} ({url}): {fallback_e}")
                        # Create error result
                        error_result = type('JiniCrawlResult', (), {
                            'success': False,
                            'url': url,
                            'error': str(fallback_e),
                            'processed_content': f'Error processing {url}: {str(fallback_e)}',
                            'raw_content': '',
                            'title': 'Processing Error'
                        })()
                        batch_results.append(error_result)

        # Format results
        results = []
        for result in batch_results:
            results.append({
                "url": result.url,
                "success": result.success,
                "content": result.processed_content or result.raw_content or "",
                "title": result.title or "",
                "error": result.error if not result.success else None
            })

        processing_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": True,
            "results": results,
            "total_urls": len(final_urls),
            "original_urls": len(request.urls),
            "rss_discovery_enabled": request.auto_discover_rss,
            "processing_time": processing_time
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "results": []
        }

@app.post("/jina_crawler/crawl_news")
async def crawl_news(request: BatchCrawlRequest):
    """
    📰 Specialized endpoint for news crawling with auto RSS discovery

    This endpoint:
    1. Automatically discovers RSS feeds from news websites
    2. Prefers RSS feeds over homepage content
    3. Optimized for news aggregation use cases
    4. Uses batch processing for efficiency
    """
    # Override settings for news crawling
    news_request = BatchCrawlRequest(
        urls=request.urls,
        method=request.method,
        max_content_length=request.max_content_length,
        max_concurrent=request.max_concurrent,
        quality_mode=request.quality_mode,
        processing_mode=request.processing_mode,
        auto_discover_rss=True,  # Always enable for news
        prefer_rss=True,         # Always prefer RSS for news
        context="news_aggregation"  # Set context
    )

    # Use the same logic as batch crawl
    return await crawl_batch(news_request)

@app.post("/jina_crawler/discover_rss")
async def discover_rss(request: RSSDiscoveryRequest):
    """
    🔍 Discover RSS feeds from websites and optionally crawl them

    This endpoint:
    1. Analyzes websites to find RSS feeds
    2. Discovers feeds through multiple methods
    3. Optionally crawls discovered RSS feeds
    4. Returns comprehensive RSS discovery results
    """
    start_time = datetime.now()

    try:
        logger.info(f"🔍 Starting RSS discovery for {len(request.urls)} URLs")

        # Initialize crawler
        crawler = JiniCrawler()

        discovery_results = []
        all_rss_feeds = []

        # Step 1: Discover RSS feeds for each URL
        for i, url in enumerate(request.urls, 1):
            try:
                logger.info(f"🔍 Discovering RSS feeds for URL {i}/{len(request.urls)}: {url}")

                # Discover RSS feeds from the website
                rss_feeds = await discover_rss_feeds(url, max_feeds=request.max_rss_per_site)

                discovery_result = {
                    "url": url,
                    "success": True,
                    "rss_feeds": rss_feeds,
                    "total_feeds": len(rss_feeds),
                    "error": None
                }

                discovery_results.append(discovery_result)
                all_rss_feeds.extend(rss_feeds)

                logger.info(f"✅ Found {len(rss_feeds)} RSS feeds for {url}")

            except Exception as e:
                logger.error(f"❌ RSS discovery error for {url}: {e}")
                discovery_result = {
                    "url": url,
                    "success": False,
                    "rss_feeds": [],
                    "total_feeds": 0,
                    "error": str(e)
                }
                discovery_results.append(discovery_result)

        # Step 2: Optionally crawl discovered RSS feeds
        rss_content_results = []
        if request.auto_crawl_rss and all_rss_feeds:
            logger.info(f"🔄 Auto-crawling {len(all_rss_feeds)} discovered RSS feeds")

            # Create batch crawl request for RSS feeds
            rss_urls = [feed["url"] for feed in all_rss_feeds]

            # Use batch processing for RSS feeds
            if request.processing_mode == "batch":
                try:
                    # Crawl RSS feeds in batch
                    batch_raw_data = await crawler.crawl_batch_raw(
                        urls=rss_urls,
                        max_content_length=100000,  # RSS feeds are usually smaller
                        max_concurrent=6
                    )

                    # Process with Gemini
                    force_detailed = (request.quality_mode == "detailed")
                    rss_content_results = await crawler.process_batch_with_gemini(
                        batch_raw_data,
                        force_detailed=force_detailed
                    )
                    logger.info(f"✅ Batch processed {len(rss_content_results)} RSS feeds")

                except Exception as e:
                    logger.error(f"❌ RSS batch processing failed: {e}")
                    # Fallback to individual processing
                    for rss_url in rss_urls:
                        try:
                            result = await crawler.crawl_and_process(
                                url=rss_url,
                                max_content_length=100000
                            )
                            rss_content_results.append(result)
                        except Exception as individual_e:
                            logger.error(f"❌ Individual RSS processing failed for {rss_url}: {individual_e}")
            else:
                # Individual processing for RSS feeds
                for rss_url in rss_urls:
                    try:
                        result = await crawler.crawl_and_process(
                            url=rss_url,
                            max_content_length=100000
                        )
                        rss_content_results.append(result)
                    except Exception as e:
                        logger.error(f"❌ RSS processing failed for {rss_url}: {e}")

        processing_time = (datetime.now() - start_time).total_seconds()

        response_data = {
            "success": True,
            "discovery_results": discovery_results,
            "total_sites": len(request.urls),
            "total_rss_feeds": len(all_rss_feeds),
            "rss_feeds": all_rss_feeds,
            "rss_content_results": rss_content_results if request.auto_crawl_rss else [],
            "auto_crawled": request.auto_crawl_rss,
            "processing_time": processing_time
        }

        logger.info(f"✅ RSS discovery completed: {len(all_rss_feeds)} feeds found, {len(rss_content_results)} crawled")
        return response_data

    except Exception as e:
        logger.error(f"❌ RSS discovery error: {e}")
        return {
            "success": False,
            "error": str(e),
            "discovery_results": [],
            "total_sites": len(request.urls),
            "total_rss_feeds": 0,
            "rss_feeds": [],
            "rss_content_results": [],
            "auto_crawled": False,
            "processing_time": (datetime.now() - start_time).total_seconds()
        }

@app.post("/jina_crawler/bypass_paywall")
async def bypass_paywall(request: PaywallBypassRequest):
    """Bypass paywall and extract content - MCP compatible endpoint"""
    if not paywall_crawler:
        return {
            "success": False,
            "error": "Paywall Crawler not available",
            "content": f"Mock paywall bypass result for: {request.url}"
        }

    try:
        start_time = datetime.now()

        result = await paywall_crawler.bypass_and_extract(
            url=request.url,
            max_content_length=request.max_content_length
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": result.success,
            "content": result.processed_content or result.raw_content or "",
            "title": result.title or "",
            "url": result.url,
            "processing_time": processing_time,
            "error": result.error if not result.success else None
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": request.url,
            "content": f"Error bypassing paywall for {request.url}: {str(e)}"
        }

@app.post("/jina_crawler/crawl_full_article")
async def crawl_full_article(request: FullArticleRequest):
    """Crawl full article with complete content and metadata - MCP compatible endpoint"""
    if not crawler:
        return {
            "success": False,
            "error": "Crawler not available",
            "content": f"Mock full article result for: {request.url}"
        }

    try:
        start_time = datetime.now()

        # Initialize crawler if needed
        if not crawler._initialized:
            await crawler.initialize()

        # Use the full article method for complete content
        result = await crawler.crawl_full_article(
            url=request.url,
            max_content_length=request.max_content_length
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        response = {
            "success": result.success,
            "content": result.processed_content or result.raw_content or "",
            "title": result.title or "",
            "url": result.url,
            "processing_time": processing_time,
            "method": "full_article",
            "error": result.error if not result.success else None
        }

        # Add metadata if requested and available
        if request.extract_metadata and hasattr(result, 'metadata'):
            response["metadata"] = result.metadata

        return response

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": request.url,
            "content": f"Error crawling full article for {request.url}: {str(e)}"
        }

@app.post("/jina_crawler/news_summary")
async def news_summary(request: NewsSummaryRequest):
    """Create comprehensive news summary with analysis - MCP compatible endpoint"""
    if not crawler:
        return {
            "success": False,
            "error": "Crawler not available",
            "content": f"Mock news summary result for: {request.url}"
        }

    try:
        start_time = datetime.now()

        # Initialize crawler if needed
        if not crawler._initialized:
            await crawler.initialize()

        # Use enhanced processing for news summary
        result = await crawler.crawl_and_process_with_task(
            url=request.url,
            max_content_length=request.max_content_length,
            task_type="news_summary"
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        response = {
            "success": result.success,
            "content": result.processed_content or result.raw_content or "",
            "title": result.title or "",
            "url": result.url,
            "processing_time": processing_time,
            "method": "news_summary",
            "error": result.error if not result.success else None
        }

        # Add analysis if requested and available
        if request.include_analysis and hasattr(result, 'analysis'):
            response["analysis"] = result.analysis

        return response

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": request.url,
            "content": f"Error creating news summary for {request.url}: {str(e)}"
        }

@app.post("/jina_crawler/enhanced_extraction")
async def enhanced_extraction(request: EnhancedExtractionRequest):
    """Enhanced content extraction with detailed analysis - MCP compatible endpoint"""
    if not crawler:
        return {
            "success": False,
            "error": "Crawler not available",
            "content": f"Mock enhanced extraction result for: {request.url}"
        }

    try:
        start_time = datetime.now()

        # Initialize crawler if needed
        if not crawler._initialized:
            await crawler.initialize()

        # Use enhanced extraction processing
        result = await crawler.crawl_and_process_with_task(
            url=request.url,
            max_content_length=request.max_content_length,
            task_type="enhanced_extraction"
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        response = {
            "success": result.success,
            "content": result.processed_content or result.raw_content or "",
            "title": result.title or "",
            "url": result.url,
            "processing_time": processing_time,
            "method": "enhanced_extraction",
            "error": result.error if not result.success else None
        }

        # Add metadata and analysis if requested
        if request.extract_metadata and hasattr(result, 'metadata'):
            response["metadata"] = result.metadata

        if request.include_analysis and hasattr(result, 'analysis'):
            response["analysis"] = result.analysis

        return response

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "url": request.url,
            "content": f"Error in enhanced extraction for {request.url}: {str(e)}"
        }

@app.post("/jina_crawler/ai_search")
async def ai_search(request: SearchRequest):
    """Multi-Source AI-powered search with comprehensive processing - Full AI pipeline with 3 parallel sources (Google PSE + DuckDuckGo + Brave) and reranking"""
    if not ai_search_engine:
        return {
            "success": False,
            "error": "AI Search Engine not available",
            "results": [{"title": f"Mock AI search result for: {request.query}", "url": "https://example.com", "snippet": "Mock AI processed content"}]
        }

    try:
        start_time = datetime.now()

        # Use full AI search pipeline with query refinement and content synthesis
        result = await ai_search_engine.search(
            query=request.query,
            enable_query_refinement=request.enable_query_refinement,
            search_type="text"
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        if result.success:
            # Format comprehensive AI search results
            formatted_results = []

            # Include search results if available
            if result.search_response and result.search_response.results:
                for search_result in result.search_response.results:
                    formatted_results.append({
                        "title": search_result.title,
                        "url": search_result.url,
                        "snippet": search_result.snippet,
                        "source": search_result.source
                    })

            # Include crawled and synthesized content if available
            ai_summary = None
            if result.synthesized_answer:
                ai_summary = {
                    "answer": result.synthesized_answer.answer,
                    "confidence": result.synthesized_answer.confidence,
                    "word_count": result.synthesized_answer.word_count,
                    "sources_used": result.synthesized_answer.sources_used,
                    "citations": [
                        {
                            "url": citation.url,
                            "title": citation.title,
                            "snippet": citation.snippet,
                            "relevance_score": citation.relevance_score
                        } for citation in result.synthesized_answer.citations
                    ]
                }

            return {
                "success": True,
                "query": request.query,
                "refined_query": result.refined_query.refined_query if result.refined_query else request.query,
                "results": formatted_results,
                "ai_summary": ai_summary,
                "processing_time": processing_time,
                "total_time": result.total_time,
                "search_sources": result.search_response.source_stats if result.search_response else {},
                "total_search_results": result.search_response.total_results if result.search_response else 0,
                "ai_processed": True,
                "reranked": True,
                "search_engines": ["google_pse", "duckduckgo", "brave"],
                "features": ["multi_source_search", "query_refinement", "content_crawling", "ai_synthesis", "gemini_reranking"]
            }
        else:
            return {
                "success": False,
                "error": result.error or "AI Search failed",
                "query": request.query,
                "results": []
            }

    except Exception as e:
        return {
            "success": False,
            "error": f"AI Search failed: {str(e)}",
            "query": request.query,
            "results": []
        }

async def discover_rss_feeds(url: str, max_feeds: int = 5) -> List[Dict[str, str]]:
    """
    🔍 Discover RSS feeds from a website using multiple methods

    Methods:
    1. Parse HTML for <link> tags with RSS/Atom types
    2. Check common RSS paths (/rss, /feed, /rss.xml, etc.)
    3. Look for RSS links in the page content
    """
    discovered_feeds = []
    base_domain = f"{urlparse(url).scheme}://{urlparse(url).netloc}"

    try:
        async with aiohttp.ClientSession() as session:
            # Method 1: Parse HTML for RSS links
            try:
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        html_content = await response.text()

                        # Find RSS/Atom links in HTML
                        rss_patterns = [
                            r'<link[^>]*type=["\']application/rss\+xml["\'][^>]*href=["\']([^"\']+)["\']',
                            r'<link[^>]*type=["\']application/atom\+xml["\'][^>]*href=["\']([^"\']+)["\']',
                            r'<link[^>]*href=["\']([^"\']*rss[^"\']*)["\'][^>]*type=["\']application/rss\+xml["\']',
                            r'<link[^>]*href=["\']([^"\']*atom[^"\']*)["\'][^>]*type=["\']application/atom\+xml["\']',
                            r'href=["\']([^"\']*\.rss)["\']',
                            r'href=["\']([^"\']*\.xml)["\']',
                            r'href=["\']([^"\']*feed[^"\']*)["\']'
                        ]

                        for pattern in rss_patterns:
                            matches = re.findall(pattern, html_content, re.IGNORECASE)
                            for match in matches:
                                feed_url = urljoin(url, match)
                                if feed_url not in [f["url"] for f in discovered_feeds]:
                                    discovered_feeds.append({
                                        "url": feed_url,
                                        "title": f"RSS Feed from {urlparse(url).netloc}",
                                        "type": "rss",
                                        "discovery_method": "html_parsing"
                                    })
                                    if len(discovered_feeds) >= max_feeds:
                                        break
                            if len(discovered_feeds) >= max_feeds:
                                break

            except Exception as e:
                logger.warning(f"⚠️ HTML parsing failed for {url}: {e}")

            # Method 2: Check common RSS paths
            if len(discovered_feeds) < max_feeds:
                common_rss_paths = [
                    "/rss",
                    "/rss.xml",
                    "/feed",
                    "/feed.xml",
                    "/feeds",
                    "/atom.xml",
                    "/rss/news",
                    "/rss/all",
                    "/index.xml",
                    "/rss/tin-moi-nhat.rss",  # VnExpress specific
                    "/rss/thoi-su.rss",       # VnExpress specific
                    "/rss/home.rss",          # Dantri specific
                    "/rss/xa-hoi.rss",        # Dantri specific
                    "/rss/thoi-su.rss",       # Tuoitre specific
                    "/rss/tin-nong.rss",      # Common Vietnamese
                    "/rss/tin-tuc.rss",       # Common Vietnamese
                    "/rss/latest.rss",        # Common English
                    "/feeds/all.xml",
                    "/feeds/latest.xml",
                    "/blog/rss",
                    "/blog/feed",
                    "/news/rss",
                    "/news/feed"
                ]

                for path in common_rss_paths:
                    if len(discovered_feeds) >= max_feeds:
                        break

                    try:
                        feed_url = urljoin(base_domain, path)
                        async with session.head(feed_url, timeout=5) as response:
                            if response.status == 200:
                                content_type = response.headers.get('content-type', '').lower()
                                if any(rss_type in content_type for rss_type in ['xml', 'rss', 'atom']):
                                    if feed_url not in [f["url"] for f in discovered_feeds]:
                                        discovered_feeds.append({
                                            "url": feed_url,
                                            "title": f"RSS Feed - {path}",
                                            "type": "rss",
                                            "discovery_method": "common_paths"
                                        })
                    except:
                        continue

        logger.info(f"🔍 Discovered {len(discovered_feeds)} RSS feeds for {url}")
        return discovered_feeds[:max_feeds]

    except Exception as e:
        logger.error(f"❌ RSS discovery failed for {url}: {e}")
        return []

if __name__ == "__main__":
    print("🚀 Starting Jina Crawler MCP Proxy Server on port 8002")
    print("📋 Compatible with Open WebUI tool system")
    print("🔗 Following MCPO pattern for tool discovery")
    uvicorn.run(app, host="0.0.0.0", port=8002)
