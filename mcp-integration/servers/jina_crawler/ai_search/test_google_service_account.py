#!/usr/bin/env python3
"""
Test Google PSE service with service account
"""

import asyncio
import logging
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Service account info
SERVICE_ACCOUNT_INFO = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

async def test_google_pse_with_service_account():
    """Test Google PSE service with service account"""
    logger.info("🧪 Testing Google PSE Service with Service Account...")
    
    try:
        # Import the service
        import google_pse_service
        GooglePSEService = google_pse_service.GooglePSEService
        
        # Create service with service account
        service = GooglePSEService(service_account_info=SERVICE_ACCOUNT_INFO)
        
        await service.initialize()
        
        # Test search
        result = await service.search("artificial intelligence news", count=5)
        
        logger.info(f"✅ Google PSE Test Results:")
        logger.info(f"   Success: {result.success}")
        logger.info(f"   Results: {result.total_results}")
        logger.info(f"   Search time: {result.search_time:.2f}s")
        
        if result.results:
            logger.info("   Top results:")
            for i, res in enumerate(result.results, 1):
                logger.info(f"     {i}. {res.title}")
                logger.info(f"        {res.url}")
                logger.info(f"        {res.snippet[:100]}...")
                logger.info("")
        
        if result.error:
            logger.error(f"   Error: {result.error}")
        
        return result.success
        
    except Exception as e:
        logger.error(f"❌ Google PSE test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'service' in locals():
            await service.cleanup()

async def test_multi_source_with_service_account():
    """Test multi-source search with service account"""
    logger.info("🧪 Testing Multi-Source Search with Service Account...")
    
    try:
        # Import the service
        import multi_source_search_service
        MultiSourceSearchService = multi_source_search_service.MultiSourceSearchService
        
        # Create service with service account for Google
        service = MultiSourceSearchService(
            google_api_key=None,  # Not using API key
            google_cse_id=None,   # Will use default
            gemini_api_key=None,  # Disable reranking for now
            enable_reranking=False
        )
        
        # Manually set service account info for Google service
        service.google_service = None  # Will be created in initialize
        
        # Override the initialization to use service account
        async def custom_initialize():
            logger.info("🚀 Initializing multi-source search service...")
            
            # Initialize Google PSE service with service account
            import google_pse_service
            GooglePSEService = google_pse_service.GooglePSEService
            service.google_service = GooglePSEService(service_account_info=SERVICE_ACCOUNT_INFO)
            await service.google_service.initialize()
            
            # Initialize DuckDuckGo service
            import search_engine_service
            SearchEngineService = search_engine_service.SearchEngineService
            service.ddg_service = SearchEngineService(max_results=8)
            await service.ddg_service.initialize()
            
            # Initialize Brave service
            import brave_search_service
            BraveSearchService = brave_search_service.BraveSearchService
            service.brave_service = BraveSearchService()
            await service.brave_service.initialize()
            
            service._initialized = True
            logger.info("✅ Multi-source search service initialized")
            return True
        
        # Use custom initialization
        await custom_initialize()
        
        # Test search
        result = await service.search("artificial intelligence latest developments")
        
        logger.info(f"✅ Multi-Source Search Test Results:")
        logger.info(f"   Success: {result.success}")
        logger.info(f"   Total results: {result.total_results}")
        logger.info(f"   Search time: {result.search_time:.2f}s")
        logger.info(f"   Source stats: {result.source_stats}")
        
        if result.results:
            logger.info("   Results by source:")
            source_counts = {}
            for res in result.results:
                source = res.source
                source_counts[source] = source_counts.get(source, 0) + 1
            
            for source, count in source_counts.items():
                logger.info(f"     {source}: {count} results")
            
            logger.info("   Top 5 results:")
            for i, res in enumerate(result.results[:5], 1):
                logger.info(f"     {i}. [{res.source}] {res.title[:50]}...")
                logger.info(f"        {res.url}")
        
        if result.error:
            logger.error(f"   Error: {result.error}")
        
        return result.success
        
    except Exception as e:
        logger.error(f"❌ Multi-source search test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'service' in locals():
            await service.cleanup()

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Google PSE Tests with Service Account...")
    
    tests = [
        ("Google PSE with Service Account", test_google_pse_with_service_account),
        ("Multi-Source with Service Account", test_multi_source_with_service_account),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            success = await test_func()
            results[test_name] = success
            logger.info(f"✅ {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            logger.error(f"❌ {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
    elif passed > 0:
        logger.info(f"✅ {passed} out of {total} tests working")
    else:
        logger.warning("❌ No tests working")

if __name__ == "__main__":
    asyncio.run(main())
