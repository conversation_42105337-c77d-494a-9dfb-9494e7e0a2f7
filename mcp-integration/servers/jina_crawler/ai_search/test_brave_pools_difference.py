#!/usr/bin/env python3
"""
Test để kiểm tra sự khác biệt giữa 2 pool của Brave Search
"""

import asyncio
import logging
from brave_search_service import BraveSearchService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_brave_pools_difference():
    """Test sự khác biệt giữa search pool và ai_search pool"""
    
    service = BraveSearchService()
    await service.initialize()
    
    query = "lễ duyệt binh ngày 02/09/2025 Vietnam"
    
    print(f"🔍 Testing Brave Search Pools Difference")
    print(f"Query: {query}")
    print("=" * 80)
    
    try:
        # Test search pool
        print("\n📊 SEARCH POOL:")
        print("-" * 40)
        search_results = await service._search_with_pool(
            query=query,
            pool=service.search_pool,
            search_type="web",
            count=10
        )
        
        print(f"Search Pool Results: {len(search_results)}")
        for i, result in enumerate(search_results[:5], 1):
            print(f"  {i}. {result.title[:60]}...")
            print(f"     {result.url}")
            print(f"     {result.snippet[:100]}...")
            print()
        
        # Test ai_search pool
        print("\n🤖 AI SEARCH POOL:")
        print("-" * 40)
        ai_search_results = await service._search_with_pool(
            query=query,
            pool=service.ai_search_pool,
            search_type="web", 
            count=10
        )
        
        print(f"AI Search Pool Results: {len(ai_search_results)}")
        for i, result in enumerate(ai_search_results[:5], 1):
            print(f"  {i}. {result.title[:60]}...")
            print(f"     {result.url}")
            print(f"     {result.snippet[:100]}...")
            print()
        
        # Compare results
        print("\n🔄 COMPARISON:")
        print("-" * 40)
        
        # Get URLs from both pools
        search_urls = {result.url for result in search_results}
        ai_search_urls = {result.url for result in ai_search_results}
        
        # Find overlaps and differences
        common_urls = search_urls.intersection(ai_search_urls)
        search_only = search_urls - ai_search_urls
        ai_search_only = ai_search_urls - search_urls
        
        print(f"Search Pool URLs: {len(search_urls)}")
        print(f"AI Search Pool URLs: {len(ai_search_urls)}")
        print(f"Common URLs: {len(common_urls)} ({len(common_urls)/max(len(search_urls), len(ai_search_urls))*100:.1f}%)")
        print(f"Search Pool Only: {len(search_only)}")
        print(f"AI Search Pool Only: {len(ai_search_only)}")
        
        if common_urls:
            print(f"\n✅ Common URLs ({len(common_urls)}):")
            for url in list(common_urls)[:3]:
                print(f"  - {url}")
        
        if search_only:
            print(f"\n📊 Search Pool Only ({len(search_only)}):")
            for url in list(search_only)[:3]:
                print(f"  - {url}")
        
        if ai_search_only:
            print(f"\n🤖 AI Search Pool Only ({len(ai_search_only)}):")
            for url in list(ai_search_only)[:3]:
                print(f"  - {url}")
        
        # Test with different queries
        print(f"\n" + "=" * 80)
        print("🔍 Testing with English query")
        print("=" * 80)
        
        english_query = "Vietnam National Day parade September 2 2025"
        
        # Search pool
        search_results_en = await service._search_with_pool(
            query=english_query,
            pool=service.search_pool,
            search_type="web",
            count=10
        )
        
        # AI Search pool  
        ai_search_results_en = await service._search_with_pool(
            query=english_query,
            pool=service.ai_search_pool,
            search_type="web",
            count=10
        )
        
        # Compare English results
        search_urls_en = {result.url for result in search_results_en}
        ai_search_urls_en = {result.url for result in ai_search_results_en}
        common_urls_en = search_urls_en.intersection(ai_search_urls_en)
        
        print(f"English Query: {english_query}")
        print(f"Search Pool URLs: {len(search_urls_en)}")
        print(f"AI Search Pool URLs: {len(ai_search_urls_en)}")
        print(f"Common URLs: {len(common_urls_en)} ({len(common_urls_en)/max(len(search_urls_en), len(ai_search_urls_en))*100:.1f}%)")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
    
    finally:
        await service.cleanup()

async def test_api_key_differences():
    """Test xem các API key có trả về kết quả khác nhau không"""
    
    service = BraveSearchService()
    await service.initialize()
    
    query = "Vietnam National Day 2025"
    
    print(f"\n" + "=" * 80)
    print("🔑 Testing API Key Differences")
    print("=" * 80)
    
    try:
        # Test với từng API key riêng biệt
        search_keys = service.search_pool.api_keys
        ai_search_keys = service.ai_search_pool.api_keys
        
        print(f"Search Pool Keys: {len(search_keys)}")
        print(f"AI Search Pool Keys: {len(ai_search_keys)}")
        
        # Test first key from each pool
        print(f"\n🔍 Testing individual API keys:")
        print(f"Search Key 1: {search_keys[0][:10]}...")
        print(f"AI Search Key 1: {ai_search_keys[0][:10]}...")
        
        # Note: Brave API keys are different subscription types
        # Search keys: For web search API
        # AI Search keys: For AI-powered search API (if different endpoint)
        
        print(f"\n💡 Analysis:")
        print(f"- Search Pool: Uses standard web search API keys")
        print(f"- AI Search Pool: Uses separate API keys (could be different subscription)")
        print(f"- Both pools hit the same endpoint: {service.base_url}/web/search")
        print(f"- Difference may come from:")
        print(f"  1. Different API key quotas/limits")
        print(f"  2. Different geographic routing")
        print(f"  3. Different caching on Brave's side")
        print(f"  4. Load balancing across different servers")
        
    except Exception as e:
        logger.error(f"❌ API key test failed: {e}")
    
    finally:
        await service.cleanup()

async def main():
    """Run all tests"""
    await test_brave_pools_difference()
    await test_api_key_differences()

if __name__ == "__main__":
    asyncio.run(main())
