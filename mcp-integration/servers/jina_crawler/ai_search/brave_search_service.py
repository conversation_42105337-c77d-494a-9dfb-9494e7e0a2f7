"""
Brave Search Service with API Key Rotation and Rate Limiting

Provides fallback search functionality when DuckDuckGo fails due to rate limiting.
Features separate API key pools for Search and AI Search with proper rotation.
"""

import asyncio
import logging
import time
import random
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

@dataclass
class BraveSearchResult:
    """Represents a single Brave search result"""
    title: str
    url: str
    snippet: str
    source: str = "brave"

@dataclass
class BraveSearchResponse:
    """Represents the complete Brave search response"""
    query: str
    results: List[BraveSearchResult]
    total_results: int
    search_time: float
    success: bool
    pool_used: str = ""
    api_key_used: str = ""
    error: Optional[str] = None

class BraveAPIKeyPool:
    """Manages API key rotation for a specific pool"""
    
    def __init__(self, pool_name: str, api_keys: List[str]):
        self.pool_name = pool_name
        self.api_keys = api_keys
        self.current_index = 0
        self.last_used_times = {key: 0.0 for key in api_keys}
        self.rate_limit = 1.0  # 1 request per second
        
    def get_next_key(self) -> str:
        """Get the next available API key with rate limiting"""
        current_time = time.time()
        
        # Find a key that hasn't been used recently
        for _ in range(len(self.api_keys)):
            key = self.api_keys[self.current_index]
            last_used = self.last_used_times[key]
            
            if current_time - last_used >= self.rate_limit:
                self.last_used_times[key] = current_time
                self.current_index = (self.current_index + 1) % len(self.api_keys)
                return key
            
            self.current_index = (self.current_index + 1) % len(self.api_keys)
        
        # If all keys were used recently, wait for the oldest one
        oldest_key = min(self.api_keys, key=lambda k: self.last_used_times[k])
        wait_time = self.rate_limit - (current_time - self.last_used_times[oldest_key])
        
        if wait_time > 0:
            logger.debug(f"⏳ Rate limit: waiting {wait_time:.2f}s for {self.pool_name} pool")
            return None  # Caller should wait
        
        self.last_used_times[oldest_key] = current_time
        return oldest_key
    
    async def get_available_key(self) -> str:
        """Get an available API key, waiting if necessary"""
        while True:
            key = self.get_next_key()
            if key:
                return key
            await asyncio.sleep(0.1)  # Short wait before retry

class BraveSearchService:
    """
    🔍 BRAVE SEARCH FALLBACK SERVICE
    
    Features:
    - Dual API key pools (Search + AI Search)
    - Rate limiting (1 request/s per pool)
    - Async parallel search across pools
    - Automatic fallback when DuckDuckGo fails
    """
    
    def __init__(self):
        """Initialize Brave Search Service with API key pools"""
        
        # Search API Pool
        self.search_pool = BraveAPIKeyPool(
            pool_name="search",
            api_keys=[
                "BSA9LNrv9tHWrSu8guWYu8i47Iisyr8",
                "BSAXYx3BRnMxPu_xUghx8Vw3GgG3dlW"
            ]
        )
        
        # AI Search API Pool
        self.ai_search_pool = BraveAPIKeyPool(
            pool_name="ai_search", 
            api_keys=[
                "BSAx32MoYMWXdxU9IEPWrSF6K7539Fu",
                "BSAWh8paqNo8JFJaBmvd6WX7CqT2_Sk"
            ]
        )
        
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url = "https://api.search.brave.com/res/v1"
        self.timeout = 15
        self._initialized = False
        
    async def initialize(self):
        """Initialize the Brave Search Service"""
        if not self._initialized:
            try:
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                self.session = aiohttp.ClientSession(timeout=timeout)
                self._initialized = True
                logger.info("✅ Brave Search Service initialized with dual API pools")
                logger.info(f"   📊 Search pool: {len(self.search_pool.api_keys)} keys")
                logger.info(f"   🤖 AI Search pool: {len(self.ai_search_pool.api_keys)} keys")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Brave Search Service: {e}")
                raise
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
            self.session = None
        self._initialized = False
        logger.info("✅ Brave Search Service cleanup completed")
    
    @retry(
        stop=stop_after_attempt(1),  # 🚀 AGGRESSIVE: Only 1 attempt for faster fallback
        wait=wait_exponential(multiplier=1, min=1, max=2)  # 🚀 AGGRESSIVE: Shorter wait times
    )
    async def _search_with_pool(
        self,
        query: str,
        pool: BraveAPIKeyPool,
        search_type: str = "web",
        count: int = 20  # 20 results per pool for maximum coverage
    ) -> List[BraveSearchResult]:
        """
        Perform search using a specific API key pool
        
        Args:
            query: Search query
            pool: API key pool to use
            search_type: Type of search ("web" or "news")
            count: Number of results to return
            
        Returns:
            List of BraveSearchResult objects
        """
        try:
            # Get available API key from pool
            api_key = await pool.get_available_key()
            
            # Prepare headers
            headers = {
                "Accept": "application/json",
                "Accept-Encoding": "gzip",
                "X-Subscription-Token": api_key
            }
            
            # Prepare parameters
            params = {
                "q": query,
                "count": count,
                "offset": 0,
                "mkt": "en-US",
                "safesearch": "moderate",
                "freshness": "pw"  # Past week for freshness
            }
            
            # Choose endpoint based on search type
            if search_type == "news":
                endpoint = f"{self.base_url}/news/search"
            else:
                endpoint = f"{self.base_url}/web/search"
            
            logger.debug(f"🔍 Brave {pool.pool_name} search: {query}")
            
            async with self.session.get(endpoint, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []
                    
                    # Parse web search results
                    if "web" in data and "results" in data["web"]:
                        for item in data["web"]["results"]:
                            results.append(BraveSearchResult(
                                title=item.get("title", ""),
                                url=item.get("url", ""),
                                snippet=item.get("description", ""),
                                source=f"brave_{pool.pool_name}"
                            ))
                    
                    # Parse news search results
                    elif "news" in data and "results" in data["news"]:
                        for item in data["news"]["results"]:
                            results.append(BraveSearchResult(
                                title=item.get("title", ""),
                                url=item.get("url", ""),
                                snippet=item.get("description", ""),
                                source=f"brave_{pool.pool_name}_news"
                            ))
                    
                    logger.debug(f"✅ Brave {pool.pool_name}: {len(results)} results")
                    return results
                    
                elif response.status == 429:
                    logger.warning(f"🚫 Rate limit hit for {pool.pool_name} pool")
                    raise Exception(f"Rate limit exceeded for {pool.pool_name}")
                    
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Brave {pool.pool_name} API error {response.status}: {error_text}")
                    raise Exception(f"Brave API error {response.status}: {error_text}")
                    
        except Exception as e:
            logger.error(f"❌ Brave {pool.pool_name} search failed: {e}")
            raise
    
    async def search_parallel(
        self,
        query: str,
        search_type: str = "web",
        searches_per_pool: int = 1
    ) -> BraveSearchResponse:
        """
        Perform parallel searches across both API pools
        
        Args:
            query: Search query
            search_type: Type of search ("web" or "news")  
            searches_per_pool: Number of searches per pool (default: 3)
            
        Returns:
            BraveSearchResponse with combined results from both pools
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            logger.info(f"🔍 Brave optimized search: '{query}' ({searches_per_pool} search per pool, 20 results each)")
            
            # Create search tasks for both pools
            search_tasks = []
            
            # Search pool tasks
            for i in range(searches_per_pool):
                task = self._search_with_pool(
                    query=query,
                    pool=self.search_pool,
                    search_type=search_type,
                    count=20  # 20 results per search for maximum coverage
                )
                search_tasks.append(("search", i, task))
            
            # AI Search pool tasks  
            for i in range(searches_per_pool):
                task = self._search_with_pool(
                    query=query,
                    pool=self.ai_search_pool,
                    search_type=search_type,
                    count=20  # 20 results per search for maximum coverage
                )
                search_tasks.append(("ai_search", i, task))
            
            # Execute all searches in parallel with rate limiting
            all_results = []
            successful_pools = []
            
            # Execute tasks with proper spacing to respect rate limits
            for pool_type, search_num, task in search_tasks:
                try:
                    # 🚀 AGGRESSIVE: Minimal delay for faster fallback
                    if len(all_results) > 0:
                        await asyncio.sleep(0.6)  # Reduced delay for faster response
                    
                    results = await task
                    all_results.extend(results)
                    successful_pools.append(f"{pool_type}_{search_num}")
                    
                except Exception as e:
                    logger.warning(f"⚠️ {pool_type} search {search_num} failed: {e}")
                    continue
            
            # Remove duplicates based on URL
            seen_urls = set()
            unique_results = []
            for result in all_results:
                if result.url not in seen_urls:
                    seen_urls.add(result.url)
                    unique_results.append(result)

            # Limit to 20 total results for better coverage
            final_results = unique_results[:20]

            search_time = time.time() - start_time

            # Detailed logging for debugging
            logger.info(f"✅ Brave parallel search completed in {search_time:.2f}s")
            logger.info(f"   📊 Raw results: {len(all_results)} from {len(successful_pools)} pools")
            logger.info(f"   🔄 After deduplication: {len(unique_results)} unique results")
            logger.info(f"   📤 Final results: {len(final_results)} (limited to 20)")
            logger.info(f"   🏊 Successful pools: {', '.join(successful_pools)}")
            
            return BraveSearchResponse(
                query=query,
                results=final_results,
                total_results=len(final_results),
                search_time=search_time,
                success=len(final_results) > 0,
                pool_used=f"both_pools_{len(successful_pools)}_searches",
                api_key_used="rotated"
            )
            
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"❌ Brave parallel search failed: {e}")
            
            return BraveSearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                error=str(e)
            )
    
    async def search_single_pool(
        self,
        query: str,
        pool_name: str = "search",
        search_type: str = "web",
        count: int = 10
    ) -> BraveSearchResponse:
        """
        Perform search using a single pool (for testing or specific needs)
        
        Args:
            query: Search query
            pool_name: Pool to use ("search" or "ai_search")
            search_type: Type of search ("web" or "news")
            count: Number of results to return
            
        Returns:
            BraveSearchResponse with results from specified pool
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        
        try:
            pool = self.search_pool if pool_name == "search" else self.ai_search_pool
            results = await self._search_with_pool(query, pool, search_type, count)
            
            search_time = time.time() - start_time
            
            return BraveSearchResponse(
                query=query,
                results=results,
                total_results=len(results),
                search_time=search_time,
                success=True,
                pool_used=pool_name,
                api_key_used="rotated"
            )
            
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"❌ Brave {pool_name} search failed: {e}")
            
            return BraveSearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                pool_used=pool_name,
                error=str(e)
            )
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on both API pools"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Test both pools with a simple query
            test_query = "test search"
            
            # Test search pool
            search_result = await self.search_single_pool(
                query=test_query,
                pool_name="search",
                count=1
            )
            
            # Test AI search pool
            ai_search_result = await self.search_single_pool(
                query=test_query,
                pool_name="ai_search", 
                count=1
            )
            
            return {
                "status": "healthy" if (search_result.success or ai_search_result.success) else "error",
                "search_pool": {
                    "status": "healthy" if search_result.success else "error",
                    "api_keys": len(self.search_pool.api_keys),
                    "error": search_result.error
                },
                "ai_search_pool": {
                    "status": "healthy" if ai_search_result.success else "error", 
                    "api_keys": len(self.ai_search_pool.api_keys),
                    "error": ai_search_result.error
                },
                "total_api_keys": len(self.search_pool.api_keys) + len(self.ai_search_pool.api_keys)
            }
            
        except Exception as e:
            logger.error(f"❌ Brave Search health check failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

# Convenience functions
async def create_brave_search_service() -> BraveSearchService:
    """Create and initialize a Brave Search Service"""
    service = BraveSearchService()
    await service.initialize()
    return service

async def quick_brave_search(query: str, searches_per_pool: int = 3) -> BraveSearchResponse:
    """Quick Brave search with parallel pools"""
    service = BraveSearchService()
    try:
        await service.initialize()
        return await service.search_parallel(query, searches_per_pool=searches_per_pool)
    finally:
        await service.cleanup()

# Test function
async def test_brave_search():
    """Test Brave Search Service"""
    print("🧪 Testing Brave Search Service")
    print("=" * 50)
    
    service = BraveSearchService()
    await service.initialize()
    
    try:
        # Test parallel search
        test_query = "artificial intelligence news"
        result = await service.search_parallel(test_query, searches_per_pool=2)
        
        print(f"✅ Success: {result.success}")
        print(f"   Query: {result.query}")
        print(f"   Results: {result.total_results}")
        print(f"   Search time: {result.search_time:.2f}s")
        print(f"   Pools used: {result.pool_used}")
        
        if result.results:
            print("   Top 3 results:")
            for i, res in enumerate(result.results[:3], 1):
                print(f"     {i}. {res.title[:60]}... ({res.source})")
        
        # Health check
        health = await service.health_check()
        print(f"\n🏥 Health check: {health['status']}")
        print(f"   Search pool: {health['search_pool']['status']}")
        print(f"   AI Search pool: {health['ai_search_pool']['status']}")
        
    finally:
        await service.cleanup()
        print("\n✅ Test completed")

if __name__ == "__main__":
    asyncio.run(test_brave_search())