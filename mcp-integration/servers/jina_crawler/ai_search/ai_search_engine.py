"""
AI Search Engine

Main orchestrator that coordinates all services to provide end-to-end AI search functionality
similar to Perplexity. Handles the complete workflow from query refinement to content synthesis.
"""

import asyncio
import logging
import time
import sys
import os
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from utils.config import config

try:
    from .query_refinement_service import QueryRefinementService, RefinedQuery
    from .search_engine_service import SearchEngineService, SearchResponse
    from .multi_source_search_service import MultiSourceSearchService, MultiSourceSearchResponse
    from .batch_crawler_service import BatchCrawlerService, BatchCrawlResponse
    from .content_synthesis_service import ContentSynthesisService, SynthesizedAnswer
except ImportError:
    # Fallback for direct execution
    from query_refinement_service import QueryRefinementService, RefinedQuery
    from search_engine_service import SearchEngineService, SearchResponse
    from multi_source_search_service import MultiSourceSearchService, MultiSourceSearchResponse
    from batch_crawler_service import BatchCrawlerService, BatchCrawlResponse
    from content_synthesis_service import ContentSynthesisService, SynthesizedAnswer
# Import cleanup manager
try:
    from utils.cleanup_manager import cleanup_all_resources, register_cleanup_callback
except ImportError:
    # Fallback for relative import issues
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from utils.cleanup_manager import cleanup_all_resources, register_cleanup_callback

logger = logging.getLogger(__name__)

@dataclass
class AISearchResult:
    """Complete AI search result"""
    query: str
    refined_query: Optional[RefinedQuery] = None
    search_response: Optional[MultiSourceSearchResponse] = None
    crawl_response: Optional[BatchCrawlResponse] = None
    synthesized_answer: Optional[SynthesizedAnswer] = None
    total_time: float = 0.0
    success: bool = False
    error: Optional[str] = None
    metadata: Dict[str, Any] = None

class AISearchEngine:
    """
    Main AI Search Engine that orchestrates the complete search workflow
    """
    
    def __init__(
        self,
        gemini_api_key: Optional[str] = None,
        google_api_key: Optional[str] = None,
        google_cse_id: Optional[str] = None,
        max_search_results: int = 10,  # Increased from 5 to 10
        max_concurrent_crawls: int = 5,  # Increased from 3 to 5
        crawl_timeout: int = 45  # Increased from 30 to 45 seconds
    ):
        """
        Initialize the AI Search Engine

        Args:
            gemini_api_key: Google AI API key for Gemini services (if None, uses config)
            google_api_key: Google API key for PSE (if None, uses default)
            google_cse_id: Google Custom Search Engine ID
            max_search_results: Maximum number of search results to process
            max_concurrent_crawls: Maximum concurrent crawl operations
            crawl_timeout: Timeout for crawl operations
        """
        # Use provided API key or get from config
        self.gemini_api_key = gemini_api_key or config.model.gemini_api_key
        self.google_api_key = google_api_key or "AIzaSyBrTm59kxOlERAmm-oxa-U0ZTXcZykujFA"
        self.google_cse_id = google_cse_id
        self.max_search_results = max_search_results

        # Initialize services
        self.query_refinement = QueryRefinementService(api_key=self.gemini_api_key)
        self.multi_source_search = MultiSourceSearchService(
            google_api_key=self.google_api_key,
            google_service_account_info=None,  # Use API key instead
            google_cse_id=self.google_cse_id,
            gemini_api_key=self.gemini_api_key,
            enable_reranking=True
        )
        self.batch_crawler = BatchCrawlerService(
            max_concurrent=max_concurrent_crawls,
            timeout=crawl_timeout
        )
        self.content_synthesis = ContentSynthesisService(api_key=self.gemini_api_key)

        self._initialized = False
    
    async def initialize(self):
        """Initialize all services"""
        if not self._initialized:
            try:
                logger.info("🚀 Initializing AI Search Engine with multi-source search...")

                # Initialize all services concurrently
                await asyncio.gather(
                    self.query_refinement.initialize(),
                    self.multi_source_search.initialize(),
                    self.batch_crawler.initialize(),
                    self.content_synthesis.initialize()
                )

                self._initialized = True
                logger.info("✅ AI Search Engine initialized successfully with multi-source search")

            except Exception as e:
                logger.error(f"❌ Failed to initialize AI Search Engine: {e}")
                raise
    
    async def search(
        self, 
        query: str,
        enable_query_refinement: bool = True,
        search_type: str = "text"
    ) -> AISearchResult:
        """
        Perform complete AI search workflow
        
        Args:
            query: User's search query
            enable_query_refinement: Whether to refine the query first
            search_type: Type of search ("text", "news")
            
        Returns:
            AISearchResult with complete search results
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        result = AISearchResult(query=query, metadata={})
        
        try:
            logger.info(f"🔍 Starting AI search for: '{query}'")
            
            # Step 1: Query Refinement (optional)
            search_query = query
            if enable_query_refinement:
                logger.info("📝 Step 1: Refining query...")
                refinement_response = await self.query_refinement.refine_query(query)
                
                if refinement_response.success and refinement_response.refined_query:
                    result.refined_query = refinement_response.refined_query
                    search_query = refinement_response.refined_query.refined_query
                    logger.info(f"✅ Query refined: '{query}' -> '{search_query}'")
                else:
                    logger.warning(f"⚠️ Query refinement failed, using original query")
            
            # Step 2: Multi-Source Web Search
            logger.info("🌐 Step 2: Multi-source search (Google PSE + DuckDuckGo + Brave)...")
            search_response = await self.multi_source_search.search(search_query, search_type)
            result.search_response = search_response

            if not search_response.success or not search_response.results:
                result.error = "No search results found from any source"
                result.total_time = time.time() - start_time
                return result

            # Log source statistics
            logger.info(f"📊 Search sources: {search_response.source_stats}")

            # Extract URLs for crawling
            urls_to_crawl = [r.url for r in search_response.results]
            logger.info(f"📋 Found {len(urls_to_crawl)} URLs from {len(search_response.source_stats)} sources")
            
            # Step 3: Batch Crawling
            logger.info("🕷️ Step 3: Crawling content...")
            crawl_response = await self.batch_crawler.crawl_urls(
                urls=urls_to_crawl,
                query=query,
                task_type="html_to_markdown"
            )
            result.crawl_response = crawl_response
            
            if not crawl_response.success or crawl_response.successful_crawls == 0:
                result.error = "Failed to crawl any content"
                result.total_time = time.time() - start_time
                return result
            
            # Step 4: Content Synthesis
            logger.info("🤖 Step 4: Synthesizing content...")
            successful_crawls = self.batch_crawler.get_successful_results(crawl_response)
            
            synthesis_response = await self.content_synthesis.synthesize_content(
                query=query,
                crawl_results=successful_crawls,
                max_sources=self.max_search_results
            )
            
            if synthesis_response.success and synthesis_response.synthesized_answer:
                result.synthesized_answer = synthesis_response.synthesized_answer
                result.success = True
                logger.info("✅ AI search completed successfully")
            else:
                result.error = f"Content synthesis failed: {synthesis_response.error}"
            
            result.total_time = time.time() - start_time
            
            # Add metadata
            result.metadata = {
                "search_results_count": len(search_response.results),
                "crawled_urls_count": len(urls_to_crawl),
                "successful_crawls_count": crawl_response.successful_crawls,
                "total_processing_time": result.total_time,
                "query_was_refined": result.refined_query is not None,
                "search_type": search_type
            }
            
            return result
            
        except Exception as e:
            result.total_time = time.time() - start_time
            result.error = str(e)
            logger.error(f"❌ AI search failed: {e}")
            return result
    
    async def search_streaming(
        self, 
        query: str,
        callback: callable,
        enable_query_refinement: bool = True
    ):
        """
        Perform AI search with streaming updates
        
        Args:
            query: User's search query
            callback: Callback function to receive updates
            enable_query_refinement: Whether to refine the query first
        """
        if not self._initialized:
            await self.initialize()
        
        try:
            await callback({"step": "started", "message": f"Starting AI search for: '{query}'"})
            
            # Step 1: Query Refinement
            if enable_query_refinement:
                await callback({"step": "refining_query", "message": "Refining your query..."})
                refinement_response = await self.query_refinement.refine_query(query)
                
                if refinement_response.success:
                    refined_query = refinement_response.refined_query.refined_query
                    await callback({
                        "step": "query_refined", 
                        "message": f"Query refined to: '{refined_query}'"
                    })
                    search_query = refined_query
                else:
                    search_query = query
            else:
                search_query = query
            
            # Step 2: Multi-Source Web Search
            await callback({"step": "searching", "message": "Multi-source search (Google PSE + DuckDuckGo + Brave)..."})
            search_response = await self.multi_source_search.search(search_query)

            if search_response.success:
                await callback({
                    "step": "search_complete",
                    "message": f"Found {len(search_response.results)} results from {len(search_response.source_stats)} sources"
                })
            
            # Step 3: Crawling
            urls = [r.url for r in search_response.results]
            await callback({"step": "crawling", "message": f"Crawling {len(urls)} sources..."})
            
            crawl_response = await self.batch_crawler.crawl_urls(urls, query)
            
            await callback({
                "step": "crawling_complete",
                "message": f"Successfully crawled {crawl_response.successful_crawls} sources"
            })
            
            # Step 4: Synthesis
            await callback({"step": "synthesizing", "message": "Synthesizing information..."})
            
            successful_crawls = self.batch_crawler.get_successful_results(crawl_response)
            synthesis_response = await self.content_synthesis.synthesize_content(
                query, successful_crawls
            )
            
            if synthesis_response.success:
                await callback({
                    "step": "complete",
                    "result": synthesis_response.synthesized_answer
                })
            else:
                await callback({
                    "step": "error",
                    "message": f"Synthesis failed: {synthesis_response.error}"
                })
                
        except Exception as e:
            await callback({"step": "error", "message": str(e)})
    
    def to_dict(self, result: AISearchResult) -> Dict[str, Any]:
        """Convert AISearchResult to dictionary for JSON serialization"""
        return asdict(result)
    
    async def cleanup(self):
        """Cleanup all services properly to prevent resource warnings"""
        if self._initialized:
            try:
                logger.info("🧹 Starting comprehensive AI Search Engine cleanup...")

                # Cleanup all services with proper error handling
                cleanup_tasks = []

                # Cleanup all services
                if hasattr(self.batch_crawler, 'cleanup'):
                    cleanup_tasks.append(self._safe_cleanup(self.batch_crawler.cleanup(), "Batch Crawler"))

                if hasattr(self.multi_source_search, 'cleanup'):
                    cleanup_tasks.append(self._safe_cleanup(self.multi_source_search.cleanup(), "Multi-Source Search"))

                if hasattr(self.query_refinement, 'cleanup'):
                    cleanup_tasks.append(self._safe_cleanup(self.query_refinement.cleanup(), "Query Refinement"))

                if hasattr(self.content_synthesis, 'cleanup'):
                    cleanup_tasks.append(self._safe_cleanup(self.content_synthesis.cleanup(), "Content Synthesis"))

                # Wait for all cleanup tasks to complete
                if cleanup_tasks:
                    await asyncio.gather(*cleanup_tasks, return_exceptions=True)

                # Use comprehensive cleanup manager
                await cleanup_all_resources(
                    force=True,
                    delay=0.5,
                    gc_cycles=5
                )

                self._initialized = False
                logger.info("✅ AI Search Engine cleanup completed")

            except Exception as e:
                logger.error(f"❌ AI Search Engine cleanup failed: {e}")

    async def _safe_cleanup(self, cleanup_coro, service_name: str):
        """Safely cleanup a service with error handling"""
        try:
            await cleanup_coro
            logger.debug(f"✅ {service_name} cleanup successful")
        except Exception as e:
            logger.warning(f"⚠️ {service_name} cleanup warning: {e}")

    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
