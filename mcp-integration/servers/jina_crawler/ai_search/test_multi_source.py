#!/usr/bin/env python3
"""
Test script for multi-source AI search engine
"""

import asyncio
import logging
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# Import with absolute imports
import google_pse_service
import multi_source_search_service
import ai_search_engine

GooglePSEService = google_pse_service.GooglePSEService
MultiSourceSearchService = multi_source_search_service.MultiSourceSearchService
AISearchEngine = ai_search_engine.AISearchEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_google_pse():
    """Test Google PSE service"""
    logger.info("🧪 Testing Google PSE Service...")
    
    api_key = "AIzaSyC3vjdRuvxx6BuXgYnmwTB7ujz5PdemYxc"
    service = GooglePSEService(api_key=api_key)
    
    try:
        await service.initialize()
        
        # Test search
        result = await service.search("artificial intelligence news", count=5)
        
        logger.info(f"✅ Google PSE Test Results:")
        logger.info(f"   Success: {result.success}")
        logger.info(f"   Results: {result.total_results}")
        logger.info(f"   Search time: {result.search_time:.2f}s")
        
        if result.results:
            logger.info("   Top 3 results:")
            for i, res in enumerate(result.results[:3], 1):
                logger.info(f"     {i}. {res.title[:60]}...")
                logger.info(f"        {res.url}")
        
        if result.error:
            logger.error(f"   Error: {result.error}")
        
        return result.success
        
    except Exception as e:
        logger.error(f"❌ Google PSE test failed: {e}")
        return False
    finally:
        await service.cleanup()

async def test_multi_source_search():
    """Test multi-source search service"""
    logger.info("🧪 Testing Multi-Source Search Service...")
    
    google_api_key = "AIzaSyC3vjdRuvxx6BuXgYnmwTB7ujz5PdemYxc"
    # You'll need a Gemini API key for reranking - using None for now
    gemini_api_key = None  # Replace with actual Gemini API key
    
    service = MultiSourceSearchService(
        google_api_key=google_api_key,
        gemini_api_key=gemini_api_key,
        enable_reranking=False  # Disable reranking for initial test
    )
    
    try:
        await service.initialize()
        
        # Test search
        result = await service.search("artificial intelligence latest developments")
        
        logger.info(f"✅ Multi-Source Search Test Results:")
        logger.info(f"   Success: {result.success}")
        logger.info(f"   Total results: {result.total_results}")
        logger.info(f"   Search time: {result.search_time:.2f}s")
        logger.info(f"   Source stats: {result.source_stats}")
        
        if result.results:
            logger.info("   Results by source:")
            source_counts = {}
            for res in result.results:
                source = res.source
                source_counts[source] = source_counts.get(source, 0) + 1
            
            for source, count in source_counts.items():
                logger.info(f"     {source}: {count} results")
            
            logger.info("   Top 5 results:")
            for i, res in enumerate(result.results[:5], 1):
                logger.info(f"     {i}. [{res.source}] {res.title[:50]}...")
                logger.info(f"        {res.url}")
        
        if result.error:
            logger.error(f"   Error: {result.error}")
        
        return result.success
        
    except Exception as e:
        logger.error(f"❌ Multi-source search test failed: {e}")
        return False
    finally:
        await service.cleanup()

async def test_ai_search_engine():
    """Test complete AI search engine"""
    logger.info("🧪 Testing Complete AI Search Engine...")
    
    # You'll need actual API keys
    google_api_key = "AIzaSyC3vjdRuvxx6BuXgYnmwTB7ujz5PdemYxc"
    gemini_api_key = None  # Replace with actual Gemini API key
    
    engine = AISearchEngine(
        gemini_api_key=gemini_api_key,
        google_api_key=google_api_key
    )
    
    try:
        await engine.initialize()
        
        # Test search
        result = await engine.search(
            query="What are the latest developments in artificial intelligence?",
            enable_query_refinement=False  # Disable for initial test
        )
        
        logger.info(f"✅ AI Search Engine Test Results:")
        logger.info(f"   Success: {result.success}")
        logger.info(f"   Total time: {result.total_time:.2f}s")
        
        if result.search_response:
            logger.info(f"   Search results: {result.search_response.total_results}")
            logger.info(f"   Source stats: {result.search_response.source_stats}")
        
        if result.crawl_response:
            logger.info(f"   Crawled URLs: {result.crawl_response.successful_crawls}")
        
        if result.synthesized_answer:
            logger.info(f"   Synthesized answer: {len(result.synthesized_answer.answer)} chars")
        
        if result.error:
            logger.error(f"   Error: {result.error}")
        
        return result.success
        
    except Exception as e:
        logger.error(f"❌ AI search engine test failed: {e}")
        return False
    finally:
        await engine.cleanup()

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Multi-Source AI Search Tests...")
    
    tests = [
        ("Google PSE Service", test_google_pse),
        ("Multi-Source Search", test_multi_source_search),
        ("AI Search Engine", test_ai_search_engine),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            success = await test_func()
            results[test_name] = success
            logger.info(f"✅ {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            logger.error(f"❌ {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed")

if __name__ == "__main__":
    asyncio.run(main())
