#!/usr/bin/env python3
"""
Test different CSE options to find the best one
"""

import asyncio
import aiohttp
import json

async def test_cse_options():
    """Test different CSE IDs to find working ones"""
    api_key = "AIzaSyBrTm59kxOlERAmm-oxa-U0ZTXcZykujFA"
    
    # Different CSE IDs to test
    cse_options = [
        {
            "id": "017576662512468239146:omuauf_lfve",
            "name": "Current CSE (Rutgers limited)",
            "description": "Current CSE that only searches specific domains"
        },
        {
            "id": "e25b1a6b4e4d84c9c", 
            "name": "General Web Search CSE",
            "description": "Should search entire web"
        },
        {
            "id": "000455696194071821846:1g2qeoclfwk",
            "name": "Alternative Web CSE",
            "description": "Another general web search option"
        },
        {
            "id": "partner-pub-4485318137511094:4384235138",
            "name": "Partner CSE",
            "description": "Partner-based CSE"
        }
    ]
    
    base_url = "https://www.googleapis.com/customsearch/v1"
    test_queries = [
        "artificial intelligence news",
        "lễ duyệt binh ngày 02/09/2025",
        "Vietnam National Day parade 2025"
    ]
    
    async with aiohttp.ClientSession() as session:
        for cse in cse_options:
            print(f"\n{'='*80}")
            print(f"Testing CSE: {cse['name']}")
            print(f"ID: {cse['id']}")
            print(f"Description: {cse['description']}")
            print(f"{'='*80}")
            
            for query in test_queries:
                print(f"\nQuery: '{query}'")
                print("-" * 50)
                
                params = {
                    "key": api_key,
                    "cx": cse['id'],
                    "q": query,
                    "num": 3,
                    "start": 1,
                    "safe": "medium",
                }
                
                try:
                    async with session.get(base_url, params=params) as response:
                        print(f"Status: {response.status}")
                        
                        if response.status == 200:
                            data = await response.json()
                            
                            # Print search info
                            search_info = data.get('searchInformation', {})
                            total_results = search_info.get('totalResults', '0')
                            search_time = search_info.get('searchTime', '0')
                            
                            print(f"Total results: {total_results}")
                            print(f"Search time: {search_time}s")
                            
                            items = data.get('items', [])
                            print(f"Items returned: {len(items)}")
                            
                            if items:
                                print("Results:")
                                for i, item in enumerate(items, 1):
                                    title = item.get('title', 'No title')[:60]
                                    link = item.get('link', 'No URL')
                                    print(f"  {i}. {title}...")
                                    print(f"     {link}")
                            else:
                                print("No results found")
                                
                        elif response.status == 403:
                            error_data = await response.json()
                            error_msg = error_data.get('error', {}).get('message', 'Unknown error')
                            print(f"403 Error: {error_msg}")
                            
                        else:
                            error_text = await response.text()
                            print(f"Error {response.status}: {error_text}")
                            
                except Exception as e:
                    print(f"Exception: {e}")
                
                # Small delay between queries
                await asyncio.sleep(1)

async def create_new_cse_instructions():
    """Print instructions for creating a new CSE"""
    print(f"\n{'='*80}")
    print("INSTRUCTIONS FOR CREATING NEW CUSTOM SEARCH ENGINE")
    print(f"{'='*80}")
    print("""
1. Go to: https://programmablesearchengine.google.com/
2. Click "Add" to create new search engine
3. Configuration:
   - Sites to search: Leave empty for entire web OR add specific sites
   - Language: English, Vietnamese
   - Name: "Multi-Source AI Search Engine"
   - SafeSearch: Moderate
   
4. For entire web search:
   - Don't add any specific sites
   - Enable "Search the entire web"
   
5. For Vietnamese content focus:
   - Add sites: *.vnexpress.net, *.dantri.com.vn, *.tuoitre.vn, *.thanhnien.vn
   - Still enable "Search the entire web"
   
6. After creation:
   - Copy the Search Engine ID (cx parameter)
   - Update google_pse_service.py with new CSE ID
   
7. Test with our debug script to verify it works

Current API Key: AIzaSyBrTm59kxOlERAmm-oxa-U0ZTXcZykujFA
Project: pse-search-462708
""")

async def main():
    """Run CSE tests"""
    print("🔍 Testing Different CSE Options...")
    
    await test_cse_options()
    await create_new_cse_instructions()
    
    print(f"\n{'='*80}")
    print("RECOMMENDATION")
    print(f"{'='*80}")
    print("""
Based on test results above:
1. If any CSE returns good results, update google_pse_service.py
2. If all fail, create new CSE following instructions above
3. Priority: CSE that works with both English and Vietnamese queries
4. Backup: Use multiple CSEs for different languages
""")

if __name__ == "__main__":
    asyncio.run(main())
