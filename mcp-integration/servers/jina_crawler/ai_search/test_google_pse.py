#!/usr/bin/env python3
"""
Simple test for Google PSE service
"""

import asyncio
import logging
import aiohttp
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class GoogleSearchResult:
    """Represents a single Google search result"""
    title: str
    url: str
    snippet: str
    source: str = "google"

@dataclass
class GoogleSearchResponse:
    """Response from Google PSE search"""
    query: str
    results: List[GoogleSearchResult]
    total_results: int
    search_time: float
    success: bool
    error: Optional[str] = None

class SimpleGooglePSEService:
    """Simple Google PSE service for testing"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://www.googleapis.com/customsearch/v1"
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def initialize(self):
        """Initialize the service"""
        if not self.session:
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            timeout = aiohttp.ClientTimeout(total=15)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
        logger.info("✅ Google PSE service initialized")
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("✅ Google PSE service cleaned up")
    
    async def search(self, query: str, count: int = 10) -> GoogleSearchResponse:
        """Perform Google PSE search"""
        start_time = time.time()
        
        try:
            logger.info(f"🔍 Google PSE search: '{query}' ({count} results)")
            
            # Use a general web search CSE ID (this is a public one for testing)
            cse_id = "017576662512468239146:omuauf_lfve"
            
            params = {
                "key": self.api_key,
                "cx": cse_id,
                "q": query,
                "num": min(count, 10),
                "start": 1,
                "safe": "medium",
            }
            
            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    results = self._parse_results(data)
                    search_time = time.time() - start_time
                    
                    logger.info(f"✅ Google PSE found {len(results)} results in {search_time:.2f}s")
                    
                    return GoogleSearchResponse(
                        query=query,
                        results=results,
                        total_results=len(results),
                        search_time=search_time,
                        success=True
                    )
                else:
                    error_text = await response.text()
                    raise Exception(f"Google API error {response.status}: {error_text}")
                    
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"❌ Google PSE search failed: {e}")
            
            return GoogleSearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                error=str(e)
            )
    
    def _parse_results(self, data: Dict[str, Any]) -> List[GoogleSearchResult]:
        """Parse Google API response"""
        results = []
        
        items = data.get("items", [])
        for item in items:
            try:
                result = GoogleSearchResult(
                    title=item.get("title", ""),
                    url=item.get("link", ""),
                    snippet=item.get("snippet", ""),
                    source="google"
                )
                results.append(result)
                
            except Exception as e:
                logger.warning(f"⚠️ Failed to parse Google result: {e}")
                continue
        
        return results

async def test_google_pse():
    """Test Google PSE service"""
    logger.info("🧪 Testing Google PSE Service...")
    
    api_key = "AIzaSyBrTm59kxOlERAmm-oxa-U0ZTXcZykujFA"
    service = SimpleGooglePSEService(api_key=api_key)
    
    try:
        await service.initialize()
        
        # Test search
        result = await service.search("artificial intelligence news", count=5)
        
        logger.info(f"✅ Google PSE Test Results:")
        logger.info(f"   Success: {result.success}")
        logger.info(f"   Results: {result.total_results}")
        logger.info(f"   Search time: {result.search_time:.2f}s")
        
        if result.results:
            logger.info("   Top results:")
            for i, res in enumerate(result.results, 1):
                logger.info(f"     {i}. {res.title}")
                logger.info(f"        {res.url}")
                logger.info(f"        {res.snippet[:100]}...")
                logger.info("")
        
        if result.error:
            logger.error(f"   Error: {result.error}")
        
        return result.success
        
    except Exception as e:
        logger.error(f"❌ Google PSE test failed: {e}")
        return False
    finally:
        await service.cleanup()

async def main():
    """Run test"""
    logger.info("🚀 Starting Google PSE Test...")
    
    success = await test_google_pse()
    
    if success:
        logger.info("🎉 Google PSE test passed!")
    else:
        logger.error("❌ Google PSE test failed!")

if __name__ == "__main__":
    asyncio.run(main())
