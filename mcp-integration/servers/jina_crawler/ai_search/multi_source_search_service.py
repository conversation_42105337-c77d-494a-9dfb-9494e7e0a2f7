"""
Multi-Source Search Service

Integrates multiple search engines to provide comprehensive search results:
1. Google PSE: 10 results
2. DuckDuckGo: 8 results  
3. Brave (2 pools): 2x3 = 6 results
Total: 24 results before reranking
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass

# Import search services
try:
    from .google_pse_service import GooglePSEService, GoogleSearchResult
    from .search_engine_service import SearchEngineService, SearchResult as DDGSearchResult
    from .brave_search_service import BraveSearchService, BraveSearchResult
    from .search_reranker import GeminiReranker
except ImportError:
    # Fallback for direct execution
    from google_pse_service import GooglePSEService, GoogleSearchResult
    from search_engine_service import SearchEngineService, SearchResult as DDGSearchResult
    from brave_search_service import BraveSearchService, BraveSearchResult
    from search_reranker import GeminiReranker

logger = logging.getLogger(__name__)

@dataclass
class UnifiedSearchResult:
    """Unified search result from any source"""
    title: str
    url: str
    snippet: str
    source: str
    position: int = 0  # For reranker compatibility

@dataclass
class MultiSourceSearchResponse:
    """Response from multi-source search"""
    query: str
    results: List[UnifiedSearchResult]
    total_results: int
    search_time: float
    success: bool
    source_stats: Dict[str, Dict[str, Any]]
    error: Optional[str] = None

class MultiSourceSearchService:
    """
    🌐 MULTI-SOURCE SEARCH ENGINE
    
    Integrates multiple search sources for comprehensive results:
    - Google PSE: 10 high-quality results
    - DuckDuckGo: 8 diverse results
    - Brave: 6 results from 2 pools (2x3)
    
    Features:
    - Parallel search execution
    - Result deduplication
    - Source attribution
    - Unified result format
    """
    
    def __init__(self,
                 google_api_key: str = None,
                 google_service_account_info: Dict[str, Any] = None,
                 google_cse_id: str = None,
                 gemini_api_key: str = None,
                 enable_reranking: bool = True):
        """
        Initialize multi-source search service

        Args:
            google_api_key: Google API key for PSE (optional)
            google_service_account_info: Google service account info for PSE (optional)
            google_cse_id: Google Custom Search Engine ID
            gemini_api_key: Gemini API key for reranking
            enable_reranking: Whether to enable result reranking
        """
        self.google_api_key = google_api_key
        self.google_service_account_info = google_service_account_info
        self.google_cse_id = google_cse_id
        self.gemini_api_key = gemini_api_key
        self.enable_reranking = enable_reranking

        # Initialize services
        self.google_service: Optional[GooglePSEService] = None
        self.ddg_service: Optional[SearchEngineService] = None
        self.brave_service: Optional[BraveSearchService] = None
        self.reranker: Optional[GeminiReranker] = None

        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize all search services"""
        try:
            logger.info("🚀 Initializing multi-source search service...")
            
            # Initialize Google PSE service
            if self.google_service_account_info or self.google_api_key:
                self.google_service = GooglePSEService(
                    service_account_info=self.google_service_account_info,
                    api_key=self.google_api_key,
                    search_engine_id=self.google_cse_id
                )
                await self.google_service.initialize()
            else:
                logger.warning("⚠️ No Google credentials provided, Google PSE disabled")
            
            # Initialize DuckDuckGo service
            self.ddg_service = SearchEngineService(max_results=8)
            await self.ddg_service.initialize()
            
            # Initialize Brave service
            self.brave_service = BraveSearchService()
            await self.brave_service.initialize()

            # Initialize reranker if enabled
            if self.enable_reranking and self.gemini_api_key:
                self.reranker = GeminiReranker(
                    api_key=self.gemini_api_key,
                    max_results=8,  # Rerank to top 8 results
                    min_results=3,
                    score_threshold=0.7
                )
                await self.reranker.initialize()
                logger.info("✅ Reranker initialized")

            self._initialized = True
            logger.info("✅ Multi-source search service initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize multi-source search service: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup all services"""
        if self.google_service:
            await self.google_service.cleanup()
        if self.ddg_service:
            await self.ddg_service.cleanup()
        if self.brave_service:
            await self.brave_service.cleanup()
        if self.reranker:
            await self.reranker.cleanup()
        logger.info("✅ Multi-source search service cleaned up")
    
    async def search(self, 
                    query: str,
                    search_type: str = "web") -> MultiSourceSearchResponse:
        """
        Perform multi-source search
        
        Args:
            query: Search query
            search_type: Type of search ("web" or "news")
            
        Returns:
            MultiSourceSearchResponse with combined results
        """
        if not self._initialized:
            await self.initialize()
        
        start_time = time.time()
        source_stats = {}
        all_results = []
        
        try:
            logger.info(f"🔍 Multi-source search: '{query}'")
            
            # Create search tasks for parallel execution
            search_tasks = []
            
            # Google PSE search (10 results)
            if self.google_service:
                google_task = self._search_google(query, search_type)
                search_tasks.append(("google", google_task))
            
            # DuckDuckGo search (8 results)
            if self.ddg_service:
                ddg_task = self._search_duckduckgo(query, search_type)
                search_tasks.append(("duckduckgo", ddg_task))
            
            # Brave search (2x3 = 6 results)
            if self.brave_service:
                brave_task = self._search_brave(query, search_type)
                search_tasks.append(("brave", brave_task))
            
            # Execute all searches in parallel
            logger.info(f"🚀 Executing {len(search_tasks)} parallel searches...")
            
            # Wait for all searches to complete
            search_results = await asyncio.gather(
                *[task for _, task in search_tasks],
                return_exceptions=True
            )
            
            # Process results from each source
            for i, (source_name, result) in enumerate(zip([name for name, _ in search_tasks], search_results)):
                if isinstance(result, Exception):
                    logger.error(f"❌ {source_name} search failed: {result}")
                    source_stats[source_name] = {
                        "success": False,
                        "results": 0,
                        "error": str(result)
                    }
                else:
                    # Convert to unified format
                    unified_results = self._convert_to_unified(result, source_name)
                    all_results.extend(unified_results)
                    
                    source_stats[source_name] = {
                        "success": True,
                        "results": len(unified_results),
                        "search_time": getattr(result, 'search_time', 0)
                    }
            
            # Deduplicate results by URL
            deduplicated_results = self._deduplicate_results(all_results)

            # Add position numbers for reranker
            for i, result in enumerate(deduplicated_results):
                result.position = i + 1

            # Apply reranking if enabled
            final_results = deduplicated_results
            if self.enable_reranking and self.reranker and len(deduplicated_results) > 3:
                logger.info(f"🎯 Reranking {len(deduplicated_results)} results...")
                try:
                    reranked = await self.reranker.rerank_results(query, deduplicated_results)
                    if reranked:
                        # Convert back to UnifiedSearchResult
                        final_results = []
                        for rank_result in reranked:
                            unified_result = rank_result.search_result
                            # Update with rerank info
                            if hasattr(unified_result, 'position'):
                                unified_result.position = rank_result.rank
                            final_results.append(unified_result)
                        logger.info(f"✅ Reranked to top {len(final_results)} results")
                except Exception as e:
                    logger.warning(f"⚠️ Reranking failed, using original results: {e}")
                    final_results = deduplicated_results

            search_time = time.time() - start_time

            logger.info(f"✅ Multi-source search completed: {len(final_results)} results in {search_time:.2f}s")
            
            return MultiSourceSearchResponse(
                query=query,
                results=final_results,
                total_results=len(final_results),
                search_time=search_time,
                success=True,
                source_stats=source_stats
            )
            
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"❌ Multi-source search failed: {e}")
            
            return MultiSourceSearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                success=False,
                source_stats=source_stats,
                error=str(e)
            )
    
    async def _search_google(self, query: str, search_type: str) -> Any:
        """Search Google PSE"""
        return await self.google_service.search(query, search_type, count=10)
    
    async def _search_duckduckgo(self, query: str, search_type: str) -> Any:
        """Search DuckDuckGo"""
        return await self.ddg_service.search(query, search_type)
    
    async def _search_brave(self, query: str, search_type: str) -> Any:
        """Search Brave (2 pools, 1 search each, 20 results per search)"""
        return await self.brave_service.search_parallel(query, search_type, searches_per_pool=1)
    
    def _convert_to_unified(self, result: Any, source_name: str) -> List[UnifiedSearchResult]:
        """
        Convert search results to unified format
        
        Args:
            result: Search result from any source
            source_name: Name of the source
            
        Returns:
            List of UnifiedSearchResult objects
        """
        unified_results = []
        
        try:
            if source_name == "google" and hasattr(result, 'results'):
                for res in result.results:
                    unified_results.append(UnifiedSearchResult(
                        title=res.title,
                        url=res.url,
                        snippet=res.snippet,
                        source="google"
                    ))
            
            elif source_name == "duckduckgo" and hasattr(result, 'results'):
                for res in result.results:
                    unified_results.append(UnifiedSearchResult(
                        title=res.title,
                        url=res.url,
                        snippet=res.snippet,
                        source="duckduckgo"
                    ))
            
            elif source_name == "brave" and hasattr(result, 'results'):
                for res in result.results:
                    unified_results.append(UnifiedSearchResult(
                        title=res.title,
                        url=res.url,
                        snippet=res.snippet,
                        source="brave"
                    ))
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to convert {source_name} results: {e}")
        
        return unified_results
    
    def _deduplicate_results(self, results: List[UnifiedSearchResult]) -> List[UnifiedSearchResult]:
        """
        Remove duplicate results based on URL
        
        Args:
            results: List of search results
            
        Returns:
            Deduplicated list of results
        """
        seen_urls = set()
        deduplicated = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                deduplicated.append(result)
        
        logger.info(f"🔄 Deduplicated: {len(results)} → {len(deduplicated)} results")
        return deduplicated
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all search services"""
        health_status = {
            "status": "unknown",
            "services": {}
        }
        
        try:
            # Check each service
            if self.google_service:
                health_status["services"]["google"] = await self.google_service.health_check()
            
            if self.ddg_service:
                health_status["services"]["duckduckgo"] = {"status": "available"}
            
            if self.brave_service:
                health_status["services"]["brave"] = await self.brave_service.health_check()
            
            # Overall status
            all_healthy = all(
                service.get("status") in ["healthy", "available"] 
                for service in health_status["services"].values()
            )
            
            health_status["status"] = "healthy" if all_healthy else "partial"
            
        except Exception as e:
            health_status["status"] = "error"
            health_status["error"] = str(e)
        
        return health_status

# Factory function
async def create_multi_source_search_service(
    google_api_key: str,
    google_cse_id: str = None,
    gemini_api_key: str = None,
    enable_reranking: bool = True
) -> MultiSourceSearchService:
    """Create and initialize multi-source search service"""
    service = MultiSourceSearchService(
        google_api_key=google_api_key,
        google_cse_id=google_cse_id,
        gemini_api_key=gemini_api_key,
        enable_reranking=enable_reranking
    )
    await service.initialize()
    return service
