#!/usr/bin/env python3
"""
Test multi-source search with Vietnamese query about Vietnam National Day parade
"""

import asyncio
import logging
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_vietnam_parade_search():
    """Test search for Vietnam National Day parade information"""
    logger.info("🇻🇳 Testing Vietnam National Day Parade Search...")
    
    try:
        # Import services
        import multi_source_search_service
        MultiSourceSearchService = multi_source_search_service.MultiSourceSearchService
        
        # Create service with API key
        service = MultiSourceSearchService(
            google_api_key="AIzaSyBrTm59kxOlERAmm-oxa-U0ZTXcZykujFA",
            google_service_account_info=None,
            google_cse_id=None,  # Use default
            gemini_api_key=None,  # Disable reranking for now
            enable_reranking=False
        )
        
        await service.initialize()
        
        # Test Vietnamese query
        queries = [
            "lễ duyệt binh ngày 02/09/2025",
            "Vietnam National Day parade September 2 2025",
            "lễ duyệt binh quốc khánh Việt Nam 2025"
        ]
        
        for i, query in enumerate(queries, 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"Query {i}: {query}")
            logger.info(f"{'='*60}")
            
            # Test search
            result = await service.search(query)
            
            logger.info(f"✅ Search Results for '{query}':")
            logger.info(f"   Success: {result.success}")
            logger.info(f"   Total results: {result.total_results}")
            logger.info(f"   Search time: {result.search_time:.2f}s")
            logger.info(f"   Source stats: {result.source_stats}")
            
            if result.results:
                logger.info("   Results by source:")
                source_counts = {}
                for res in result.results:
                    source = res.source
                    source_counts[source] = source_counts.get(source, 0) + 1
                
                for source, count in source_counts.items():
                    logger.info(f"     {source}: {count} results")
                
                logger.info("   Top results:")
                for j, res in enumerate(result.results[:5], 1):
                    logger.info(f"     {j}. [{res.source}] {res.title}")
                    logger.info(f"        {res.url}")
                    logger.info(f"        {res.snippet[:100]}...")
                    logger.info("")
            
            if result.error:
                logger.error(f"   Error: {result.error}")
            
            # Small delay between queries
            if i < len(queries):
                await asyncio.sleep(2)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Vietnam parade search test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'service' in locals():
            await service.cleanup()

async def test_individual_sources():
    """Test individual sources for Vietnam query"""
    logger.info("🔍 Testing Individual Sources...")
    
    query = "lễ duyệt binh ngày 02/09/2025 Vietnam"
    
    # Test Google PSE
    logger.info(f"\n--- Testing Google PSE ---")
    try:
        import google_pse_service
        GooglePSEService = google_pse_service.GooglePSEService
        
        google_service = GooglePSEService(
            api_key="AIzaSyBrTm59kxOlERAmm-oxa-U0ZTXcZykujFA"
        )
        await google_service.initialize()
        
        result = await google_service.search(query, count=5)
        logger.info(f"Google PSE: {result.total_results} results in {result.search_time:.2f}s")
        
        if result.results:
            for i, res in enumerate(result.results, 1):
                logger.info(f"  {i}. {res.title}")
                logger.info(f"     {res.url}")
        
        await google_service.cleanup()
        
    except Exception as e:
        logger.error(f"Google PSE failed: {e}")
    
    # Test Brave
    logger.info(f"\n--- Testing Brave Search ---")
    try:
        import brave_search_service
        BraveSearchService = brave_search_service.BraveSearchService
        
        brave_service = BraveSearchService()
        await brave_service.initialize()
        
        result = await brave_service.search_parallel(query, searches_per_pool=2)
        logger.info(f"Brave: {result.total_results} results in {result.search_time:.2f}s")
        
        if result.results:
            for i, res in enumerate(result.results[:3], 1):
                logger.info(f"  {i}. {res.title}")
                logger.info(f"     {res.url}")
        
        await brave_service.cleanup()
        
    except Exception as e:
        logger.error(f"Brave search failed: {e}")

async def main():
    """Run Vietnam parade search tests"""
    logger.info("🚀 Starting Vietnam National Day Parade Search Tests...")
    
    tests = [
        ("Multi-Source Vietnam Search", test_vietnam_parade_search),
        ("Individual Sources Test", test_individual_sources),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*70}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*70}")
        
        try:
            success = await test_func()
            results[test_name] = success
            logger.info(f"✅ {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            logger.error(f"❌ {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*70}")
    logger.info("VIETNAM SEARCH TEST SUMMARY")
    logger.info(f"{'='*70}")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed > 0:
        logger.info("🎉 Multi-source search working for Vietnamese queries!")
    else:
        logger.warning("❌ No tests passed")

if __name__ == "__main__":
    asyncio.run(main())
