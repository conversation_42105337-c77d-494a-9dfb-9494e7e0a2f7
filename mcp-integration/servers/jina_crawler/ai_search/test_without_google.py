#!/usr/bin/env python3
"""
Test multi-source search without Google PSE (using DuckDuckGo + Brave only)
"""

import asyncio
import logging
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_duckduckgo_search():
    """Test DuckDuckGo search directly"""
    logger.info("🧪 Testing DuckDuckGo Search...")
    
    try:
        from duckduckgo_search import DDGS
        
        query = "artificial intelligence latest news"
        logger.info(f"🔍 Searching DuckDuckGo for: '{query}'")
        
        with DDGS() as ddgs:
            results = list(ddgs.text(
                keywords=query,
                region="wt-wt",
                safesearch="moderate",
                max_results=8
            ))
        
        logger.info(f"✅ DuckDuckGo found {len(results)} results")
        
        if results:
            logger.info("   Top 3 results:")
            for i, result in enumerate(results[:3], 1):
                logger.info(f"     {i}. {result.get('title', 'No title')}")
                logger.info(f"        {result.get('href', 'No URL')}")
                logger.info(f"        {result.get('body', 'No snippet')[:100]}...")
                logger.info("")
        
        return len(results) > 0
        
    except Exception as e:
        logger.error(f"❌ DuckDuckGo test failed: {e}")
        return False

async def test_brave_search():
    """Test Brave search directly"""
    logger.info("🧪 Testing Brave Search...")
    
    try:
        # Import brave search components
        import aiohttp
        import time
        from dataclasses import dataclass
        from typing import List, Optional
        
        @dataclass
        class BraveResult:
            title: str
            url: str
            snippet: str
            source: str = "brave"
        
        # Brave API keys from the original service
        api_keys = [
            "BSA9LNrv9tHWrSu8guWYu8i47Iisyr8",
            "BSAXYx3BRnMxPu_xUghx8Vw3GgG3dlW"
        ]
        
        query = "artificial intelligence latest news"
        logger.info(f"🔍 Searching Brave for: '{query}'")
        
        # Test with first API key
        api_key = api_keys[0]
        base_url = "https://api.search.brave.com/res/v1"
        
        headers = {
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": api_key
        }
        
        params = {
            "q": query,
            "count": 6,
            "offset": 0,
            "mkt": "en-US",
            "safesearch": "moderate",
            "freshness": "pw"
        }
        
        endpoint = f"{base_url}/web/search"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(endpoint, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    results = []
                    web_results = data.get("web", {}).get("results", [])
                    
                    for item in web_results:
                        result = BraveResult(
                            title=item.get("title", ""),
                            url=item.get("url", ""),
                            snippet=item.get("description", ""),
                            source="brave"
                        )
                        results.append(result)
                    
                    logger.info(f"✅ Brave found {len(results)} results")
                    
                    if results:
                        logger.info("   Top 3 results:")
                        for i, result in enumerate(results[:3], 1):
                            logger.info(f"     {i}. {result.title}")
                            logger.info(f"        {result.url}")
                            logger.info(f"        {result.snippet[:100]}...")
                            logger.info("")
                    
                    return len(results) > 0
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Brave API error {response.status}: {error_text}")
                    return False
        
    except Exception as e:
        logger.error(f"❌ Brave test failed: {e}")
        return False

async def test_combined_search():
    """Test combined DuckDuckGo + Brave search"""
    logger.info("🧪 Testing Combined Search (DuckDuckGo + Brave)...")
    
    try:
        # Run both searches in parallel
        ddg_task = test_duckduckgo_search()
        brave_task = test_brave_search()
        
        ddg_success, brave_success = await asyncio.gather(ddg_task, brave_task)
        
        logger.info(f"✅ Combined Search Results:")
        logger.info(f"   DuckDuckGo: {'✅ Success' if ddg_success else '❌ Failed'}")
        logger.info(f"   Brave: {'✅ Success' if brave_success else '❌ Failed'}")
        
        return ddg_success or brave_success  # At least one should work
        
    except Exception as e:
        logger.error(f"❌ Combined search test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Multi-Source Search Tests (without Google PSE)...")
    
    tests = [
        ("DuckDuckGo Search", test_duckduckgo_search),
        ("Brave Search", test_brave_search),
        ("Combined Search", test_combined_search),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            success = await test_func()
            results[test_name] = success
            logger.info(f"✅ {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            logger.error(f"❌ {test_name}: CRASHED - {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed!")
    elif passed > 0:
        logger.info(f"✅ {passed} out of {total} search sources working")
    else:
        logger.warning("❌ No search sources working")

if __name__ == "__main__":
    asyncio.run(main())
