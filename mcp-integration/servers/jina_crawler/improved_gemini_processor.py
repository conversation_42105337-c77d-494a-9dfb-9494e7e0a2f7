#!/usr/bin/env python3
"""
Improved Gemini Processor with Smart Model Selection and Fallback
Handles rate limit management through intelligent model switching
"""

import os
import json
import logging
from typing import Dict, Any, Optional
import time
import asyncio
import aiohttp
from dataclasses import dataclass

# Import smart model manager
from .smart_model_manager import get_smart_model_manager, TokenEstimator

logger = logging.getLogger(__name__)

@dataclass
class GeminiConfig:
    """Configuration for Gemini API."""
    api_key: str
    model_name: str = "gemini-2.5-flash-lite"
    api_url: str = "https://generativelanguage.googleapis.com/v1beta/models"
    timeout: int = 30
    max_retries: int = 3

class ImprovedGeminiProcessor:
    """
    Improved content processor with Smart Model Selection and Fallback.
    Automatically handles rate limits by switching between models.
    """

    def __init__(self, gemini_config: Optional[GeminiConfig] = None):
        self.config = gemini_config or self._load_config()
        self.session: Optional[aiohttp.ClientSession] = None
        self.smart_manager = get_smart_model_manager()
        
        # Improved Vietnamese-optimized prompt for news extraction
        self.prompts = {
            "news_extraction": """Bạn là trợ lý chuyên tổng hợp tin tức từ các trang công nghệ và báo chí. Hãy phân tích nội dung sau và trích xuất TẤT CẢ tin tức, theo cấu trúc sau:

### [Tên chuyên mục]
- **[Tiêu đề tin]**
  [Mô tả ngắn 2–3 câu, giữ nguyên tiếng Việt]
  *(Thời gian: X giờ trước / Ngày DD/MM)* – [Xem chi tiết](URL)

---

**Yêu cầu:**
- Giữ nguyên toàn bộ tin, không lược bỏ (trừ quảng cáo, menu, footer)
- Phân theo chuyên mục: Công nghệ, Thế giới, Xe, Giải trí, Thể thao...
- Ưu tiên tin mới nhất lên đầu
- Nếu không có thời gian, ghi "(chưa rõ thời gian)"
- Nếu không có link, không thêm

**Input:**
{content}

**Output:**""",
            
            "html_to_markdown": """Convert the following HTML content to clean, well-formatted Markdown. Preserve Vietnamese text exactly, maintain proper formatting, and extract the main content while removing navigation, ads, and irrelevant elements.

HTML Content:
{content}

Markdown Output:""",
            
            "summarize": """Summarize the following Vietnamese content in a concise manner while preserving key information:

Content:
{content}

Summary:""",
            
            "clean": """Clean and format the following Vietnamese content to readable Markdown:

Content:
{content}

Cleaned Markdown:"""
        }
        
    def _load_config(self) -> GeminiConfig:
        """Load Gemini configuration from environment variables."""
        api_key = os.getenv("GEMINI_API_KEY", "AIzaSyDUnVwkztCMhNvBPG_kRARefQUghegpxNM")
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
            
        return GeminiConfig(
            api_key=api_key,
            model_name=os.getenv("GEMINI_MODEL_NAME", "gemini-2.5-flash-lite"),
            api_url=os.getenv("GEMINI_API_URL", "https://generativelanguage.googleapis.com/v1beta/models"),
            timeout=int(os.getenv("GEMINI_TIMEOUT", "30")),
            max_retries=int(os.getenv("GEMINI_MAX_RETRIES", "3"))
        )
    
    async def initialize(self) -> bool:
        """Initialize the Gemini processor."""
        try:
            if not self.session:
                timeout = aiohttp.ClientTimeout(total=self.config.timeout)
                self.session = aiohttp.ClientSession(timeout=timeout)
            
            logger.info("✅ Improved Gemini processor initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Improved Gemini processor: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("✅ Improved Gemini processor cleanup completed")
    
    async def process_content(
        self,
        content: str,
        task_type: str = "news_extraction",
        max_length: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Process content using Gemini API with improved prompt.
        """
        start_time = time.time()
        
        try:
            # Prepare prompt
            prompt = self._prepare_prompt(content, task_type, max_length)
            
            # Call Gemini API with smart model selection
            result = await self._call_gemini_api(prompt, content)
            
            processing_time = time.time() - start_time
            
            logger.info(f"Performance: improved_gemini_processing completed in {processing_time:.3f}s")
            
            return {
                "success": True,
                "original_length": len(content),
                "processed_content": result,
                "output_length": len(result),
                "processing_time": processing_time,
                "model": self.config.model_name,
                "task_type": task_type
            }
            
        except Exception as e:
            logger.error(f"Gemini processing error: {e}")
            return {
                "success": False,
                "original_length": len(content),
                "processed_content": "",
                "processing_time": 0,
                "model": self.config.model_name,
                "error": str(e),
                "output_length": 0
            }
    
    def _prepare_prompt(self, content: str, task_type: str, max_length: Optional[int]) -> str:
        """Prepare prompt for Gemini API."""
        # Smart truncation for performance
        max_content_length = max_length or 16000  # Increased for better news extraction
        if len(content) > max_content_length:
            content = self._smart_truncate(content, max_content_length)
        
        # Get appropriate prompt template
        prompt_template = self.prompts.get(task_type, self.prompts["news_extraction"])
        return prompt_template.format(content=content)
    
    def _smart_truncate(self, content: str, max_length: int) -> str:
        """Smart content truncation."""
        if len(content) <= max_length:
            return content
        
        # Try to find a good breaking point
        truncated = content[:max_length]
        
        # Vietnamese sentence endings
        vietnamese_endings = [
            '. ', '.\n', '? ', '?\n', '! ', '!\n',
            '." ', '."', '?" ', '?"', '!" ', '!"',
            '.)', '.)', '?)', '?)', '!)', '!)'
        ]
        
        # Find the last complete Vietnamese sentence within limit
        last_sentence_end = -1
        for ending in vietnamese_endings:
            pos = truncated.rfind(ending)
            if pos > last_sentence_end and pos > max_length * 0.6:  # At least 60% of content
                last_sentence_end = pos + len(ending) - 1
        
        if last_sentence_end > 0:
            return content[:last_sentence_end + 1]
        
        # Fallback: find last space to avoid cutting words
        last_space = truncated.rfind(' ')
        if last_space > max_length * 0.8:  # At least 80% of content
            return content[:last_space]
        
        return truncated + "..."
    
    async def _call_gemini_api(self, prompt: str, content: str = "") -> str:
        """Call Gemini API with smart model selection and fallback logic."""
        if not self.session:
            await self.initialize()

        # Smart model selection
        selected_model, selection_reason = self.smart_manager.select_model(content, prompt)
        logger.info(f"Selected model: {selected_model} ({selection_reason})")

        # Get model configuration
        model_config = self.smart_manager.get_model_config(selected_model)
        if not model_config:
            raise Exception(f"Unknown model: {selected_model}")

        # Estimate tokens for tracking
        estimated_tokens = TokenEstimator.estimate_tokens(prompt + content)

        url = f"{self.config.api_url}/{selected_model}:generateContent"
        params = {"key": self.config.api_key}

        headers = {
            "Content-Type": "application/json"
        }

        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.0,  # Deterministic for consistency
                "maxOutputTokens": min(8192, model_config.output_tokens_max),
                "topK": 1,
                "topP": 1.0
            }
        }

        for attempt in range(self.config.max_retries + 1):
            try:
                async with self.session.post(url, params=params, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        # Record successful usage
                        self.smart_manager.record_usage(selected_model, estimated_tokens, True, False)

                        # Extract generated text
                        if "candidates" in result and len(result["candidates"]) > 0:
                            candidate = result["candidates"][0]
                            if "content" in candidate and "parts" in candidate["content"]:
                                parts = candidate["content"]["parts"]
                                if len(parts) > 0:
                                    return parts[0].get("text", "").strip()

                        # Fallback if no content found
                        return ""

                    elif response.status == 429:
                        # Rate limited - record and try fallback
                        self.smart_manager.record_usage(selected_model, 0, False, True)
                        error_msg = f"Rate limited (429) for model {selected_model}"

                        # Check if we should fallback to another model
                        should_fallback, fallback_model = self.smart_manager.should_fallback(selected_model, error_msg)

                        if should_fallback and attempt == 0:  # Only try fallback on first attempt
                            logger.warning(f"Rate limited on {selected_model}, falling back to {fallback_model}")
                            selected_model = fallback_model
                            model_config = self.smart_manager.get_model_config(selected_model)

                            # Update URL and payload for fallback model
                            url = f"{self.config.api_url}/{selected_model}:generateContent"
                            payload["generationConfig"]["maxOutputTokens"] = min(8192, model_config.output_tokens_max)
                            continue

                        # If no fallback available or already tried, wait and retry
                        if attempt < self.config.max_retries:
                            wait_time = 2 ** attempt  # Exponential backoff
                            logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise Exception(f"Rate limited after {self.config.max_retries} retries on {selected_model}")

                    else:
                        # Other error
                        error_text = await response.text()
                        self.smart_manager.record_usage(selected_model, 0, False, False)
                        raise Exception(f"Gemini API error {response.status}: {error_text}")

            except Exception as e:
                # Check if we should fallback for this error
                should_fallback, fallback_model = self.smart_manager.should_fallback(selected_model, str(e))

                if should_fallback and attempt == 0:  # Only try fallback on first attempt
                    logger.warning(f"Error on {selected_model}, falling back to {fallback_model}: {e}")
                    selected_model = fallback_model
                    model_config = self.smart_manager.get_model_config(selected_model)

                    # Update URL and payload for fallback model
                    url = f"{self.config.api_url}/{selected_model}:generateContent"
                    payload["generationConfig"]["maxOutputTokens"] = min(8192, model_config.output_tokens_max)
                    continue

                if attempt < self.config.max_retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Gemini API call failed, waiting {wait_time}s before retry {attempt + 1}: {e}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    self.smart_manager.record_usage(selected_model, 0, False, False)
                    raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Improved Gemini processor with Smart Model Manager."""
        try:
            # Test with simple Vietnamese content
            test_content = "<h1>Tin tức công nghệ</h1><p>Trí tuệ nhân tạo đang phát triển mạnh mẽ.</p>"
            test_result = await self.process_content(test_content, "news_extraction")

            # Get usage statistics from smart manager
            usage_stats = self.smart_manager.get_usage_stats()

            return {
                "status": "healthy" if test_result.get("success") else "error",
                "model_name": self.config.model_name,
                "api_available": True,
                "test_successful": test_result.get("success", False),
                "test_processing_time": test_result.get("processing_time", 0),
                "smart_model_manager": {
                    "enabled": self.smart_manager.enable_smart_selection,
                    "primary_model": self.smart_manager.primary_model,
                    "fallback_model": self.smart_manager.fallback_model,
                    "token_threshold": self.smart_manager.token_threshold,
                    "usage_stats": usage_stats
                }
            }

        except Exception as e:
            logger.error(f"Gemini health check failed: {e}")
            return {
                "status": "error",
                "model_name": self.config.model_name,
                "api_available": False,
                "error": str(e),
                "smart_model_manager": {
                    "enabled": False,
                    "error": "Smart model manager not available"
                }
            }

# Global Improved Gemini processor instance
_improved_gemini_processor_instance = None

async def get_improved_gemini_processor() -> ImprovedGeminiProcessor:
    """Get or create global Improved Gemini processor instance."""
    global _improved_gemini_processor_instance
    
    if _improved_gemini_processor_instance is None:
        _improved_gemini_processor_instance = ImprovedGeminiProcessor()
        await _improved_gemini_processor_instance.initialize()
    
    return _improved_gemini_processor_instance

async def process_content_with_improved_gemini(
    content: str,
    task_type: str = "news_extraction",
    max_length: Optional[int] = None
) -> str:
    """
    Process content using Improved Gemini API.
    """
    processor = await get_improved_gemini_processor()
    result = await processor.process_content(content, task_type, max_length)
    
    if result.get("success"):
        return result.get("processed_content", "")
    else:
        raise Exception(f"Gemini processing failed: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    # Simple test
    async def test_improved_gemini():
        try:
            processor = ImprovedGeminiProcessor()
            await processor.initialize()
            
            test_html = """
            <html>
            <head><title>Test</title></head>
            <body>
            <h1>Chào mừng đến với trang web</h1>
            <p>Đây là một đoạn văn bản thử nghiệm bằng tiếng Việt.</p>
            </body>
            </html>
            """
            
            result = await processor.process_content(test_html, "news_extraction")
            print(f"Success: {result.get('success')}")
            print(f"Processing time: {result.get('processing_time'):.2f}s")
            print(f"Output length: {result.get('output_length')}")
            print(f"Content preview: {result.get('processed_content', '')[:200]}...")
            
            await processor.cleanup()
            
        except Exception as e:
            print(f"Error: {e}")
    
    asyncio.run(test_improved_gemini())