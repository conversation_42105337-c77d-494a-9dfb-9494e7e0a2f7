#!/usr/bin/env python3
"""
Test API server with multi-source AI search
"""

import asyncio
import aiohttp
import json
import time

async def test_api_multisource():
    """Test API server with multi-source search"""
    base_url = "http://localhost:8009"
    
    # Test queries
    test_queries = [
        {
            "query": "l<PERSON> duyệt binh ngày 02/09/2025 Việt Nam",
            "description": "Vietnamese National Day parade query"
        },
        {
            "query": "latest artificial intelligence developments 2024",
            "description": "AI developments query"
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        for i, test_case in enumerate(test_queries, 1):
            print(f"\n{'='*80}")
            print(f"Test {i}: {test_case['description']}")
            print(f"Query: {test_case['query']}")
            print(f"{'='*80}")
            
            # Prepare request
            payload = {
                "query": test_case["query"],
                "enable_query_refinement": False,  # Disable for faster testing
                "search_type": "text",
                "max_sources": 10
            }
            
            start_time = time.time()
            
            try:
                async with session.post(
                    f"{base_url}/ai_search",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)  # 2 minutes timeout
                ) as response:
                    
                    print(f"Status: {response.status}")
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        # Parse the MCP response
                        if "content" in result and result["content"]:
                            content = result["content"][0]["text"]
                            data = json.loads(content)
                            
                            print(f"✅ Success: {data.get('success', False)}")
                            print(f"   Total time: {data.get('total_time', 0):.2f}s")
                            print(f"   Search sources: {data.get('search_sources', {})}")
                            print(f"   Total search results: {data.get('total_search_results', 0)}")
                            print(f"   Word count: {data.get('word_count', 0)}")
                            print(f"   Sources used: {data.get('sources_used', 0)}")
                            print(f"   Confidence: {data.get('confidence', 0)}")
                            
                            if data.get('citations'):
                                print(f"   Citations: {len(data['citations'])}")
                                print("   Top 3 citations:")
                                for j, citation in enumerate(data['citations'][:3], 1):
                                    print(f"     {j}. {citation.get('title', 'No title')[:60]}...")
                                    print(f"        {citation.get('url', 'No URL')}")
                            
                            if data.get('answer'):
                                answer_preview = data['answer'][:200] + "..." if len(data['answer']) > 200 else data['answer']
                                print(f"   Answer preview: {answer_preview}")
                        else:
                            print(f"❌ Unexpected response format: {result}")
                    else:
                        error_text = await response.text()
                        print(f"❌ HTTP Error {response.status}: {error_text}")
                        
            except Exception as e:
                print(f"❌ Request failed: {e}")
            
            elapsed = time.time() - start_time
            print(f"   Request time: {elapsed:.2f}s")
            
            # Small delay between tests
            if i < len(test_queries):
                print("\nWaiting 5 seconds before next test...")
                await asyncio.sleep(5)

async def test_health_check():
    """Test health check endpoint"""
    print(f"\n{'='*80}")
    print("Testing Health Check")
    print(f"{'='*80}")
    
    base_url = "http://localhost:8002"
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{base_url}/health_check", json={}) as response:
                print(f"Status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Health check passed")
                    print(f"   Response: {json.dumps(result, indent=2)}")
                else:
                    error_text = await response.text()
                    print(f"❌ Health check failed: {error_text}")
                    
        except Exception as e:
            print(f"❌ Health check request failed: {e}")

async def main():
    """Run all tests"""
    print("🚀 Testing Multi-Source AI Search API Server")
    print("=" * 80)
    print("Make sure the API server is running on port 8002:")
    print("  python api_server.py")
    print("=" * 80)
    
    # Test health check first
    await test_health_check()
    
    # Test multi-source AI search
    await test_api_multisource()
    
    print(f"\n{'='*80}")
    print("🎉 Multi-Source AI Search API Testing Complete!")
    print(f"{'='*80}")

if __name__ == "__main__":
    asyncio.run(main())
