#!/usr/bin/env python3
"""
Test script for Smart Model Manager
Tests token estimation, model selection, and fallback mechanisms
"""

import os
import sys
import asyncio
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from smart_model_manager import SmartModelManager, TokenEstimator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_token_estimation():
    """Test token estimation functionality"""
    print("=" * 60)
    print("TESTING TOKEN ESTIMATION")
    print("=" * 60)
    
    test_cases = [
        ("Hello world", "english"),
        ("Xin chào thế giới", "vietnamese"),
        ("<h1>Tin tức công nghệ</h1><p>AI đang phát triển mạnh</p>", "html"),
        ("Mixed content with English and tiếng Việt", "mixed")
    ]
    
    for content, content_type in test_cases:
        tokens = TokenEstimator.estimate_tokens(content, content_type)
        detected_type = TokenEstimator.detect_content_type(content)
        print(f"Content: {content[:50]}...")
        print(f"  Type: {content_type} (detected: {detected_type})")
        print(f"  Chars: {len(content)}, Estimated tokens: {tokens}")
        print()

def test_model_selection():
    """Test model selection logic"""
    print("=" * 60)
    print("TESTING MODEL SELECTION")
    print("=" * 60)
    
    # Set test API key
    os.environ['GEMINI_API_KEY'] = 'test_key'
    
    manager = SmartModelManager('test_key')
    
    test_cases = [
        ("Short content", ""),
        ("Medium content " * 1000, ""),
        ("Very long content " * 10000, ""),
        ("AI search query with lots of crawled data " * 5000, "Comprehensive analysis prompt")
    ]
    
    for content, prompt in test_cases:
        model, reason = manager.select_model(content, prompt)
        tokens = TokenEstimator.estimate_tokens(content + prompt)
        print(f"Content length: {len(content)} chars")
        print(f"Estimated tokens: {tokens}")
        print(f"Selected model: {model}")
        print(f"Reason: {reason}")
        print()

def test_usage_tracking():
    """Test usage tracking and rate limit simulation"""
    print("=" * 60)
    print("TESTING USAGE TRACKING")
    print("=" * 60)
    
    manager = SmartModelManager('test_key')
    
    # Simulate some usage
    manager.record_usage('gemini-2.5-flash-lite', 50000, True, False)
    manager.record_usage('gemini-2.5-flash-lite', 100000, True, False)
    manager.record_usage('gemini-2.5-flash-lite', 0, False, True)  # Rate limited
    
    manager.record_usage('gemini-2.0-flash', 200000, True, False)
    
    # Get stats
    stats = manager.get_usage_stats()
    
    for model_name, model_stats in stats.items():
        print(f"Model: {model_name}")
        print(f"  Tokens used: {model_stats['tokens_used']}")
        print(f"  Requests made: {model_stats['requests_made']}")
        print(f"  Rate limited: {model_stats['rate_limited_count']}")
        print(f"  Tokens remaining: {model_stats['tokens_remaining']}")
        print()

def test_fallback_logic():
    """Test fallback decision logic"""
    print("=" * 60)
    print("TESTING FALLBACK LOGIC")
    print("=" * 60)
    
    manager = SmartModelManager('test_key')
    
    test_errors = [
        ("Rate limit error: 429 Too Many Requests", "gemini-2.5-flash-lite"),
        ("Quota exceeded", "gemini-2.5-flash-lite"),
        ("Network timeout", "gemini-2.5-flash-lite"),
        ("Rate limit error: 429 Too Many Requests", "gemini-2.0-flash"),
        ("Invalid API key", "gemini-2.5-flash-lite")
    ]
    
    for error_msg, model in test_errors:
        should_fallback, fallback_model = manager.should_fallback(model, error_msg)
        print(f"Error: {error_msg}")
        print(f"Current model: {model}")
        print(f"Should fallback: {should_fallback}")
        if should_fallback:
            print(f"Fallback to: {fallback_model}")
        print()

async def test_integration():
    """Test integration with actual processor (if API key available)"""
    print("=" * 60)
    print("TESTING INTEGRATION")
    print("=" * 60)
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key or api_key == 'test_key':
        print("No real API key found. Skipping integration test.")
        print("Set GEMINI_API_KEY environment variable to test with real API.")
        return
    
    try:
        from improved_gemini_processor import ImprovedGeminiProcessor
        
        processor = ImprovedGeminiProcessor()
        await processor.initialize()
        
        # Test with small content (should use primary model)
        small_content = "<h1>Test</h1><p>Small content for testing</p>"
        result = await processor.process_content(small_content, "clean")
        
        print("Small content test:")
        print(f"  Success: {result['success']}")
        print(f"  Processing time: {result.get('processing_time', 0):.3f}s")
        print()
        
        # Test with large content (should use fallback model)
        large_content = "<h1>Large Content</h1>" + "<p>Large content for testing. " * 2000 + "</p>"
        result = await processor.process_content(large_content, "clean")
        
        print("Large content test:")
        print(f"  Success: {result['success']}")
        print(f"  Processing time: {result.get('processing_time', 0):.3f}s")
        print()
        
        # Get usage statistics
        stats = processor.smart_manager.get_usage_stats()
        print("Usage statistics after tests:")
        for model_name, model_stats in stats.items():
            print(f"  {model_name}: {model_stats['tokens_used']} tokens used")
        
    except ImportError as e:
        print(f"Could not import processor: {e}")
    except Exception as e:
        print(f"Integration test failed: {e}")

def main():
    """Run all tests"""
    print("🧪 SMART MODEL MANAGER TEST SUITE")
    print("=" * 60)
    
    test_token_estimation()
    test_model_selection()
    test_usage_tracking()
    test_fallback_logic()
    
    # Run integration test
    asyncio.run(test_integration())
    
    print("=" * 60)
    print("✅ ALL TESTS COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    main()
